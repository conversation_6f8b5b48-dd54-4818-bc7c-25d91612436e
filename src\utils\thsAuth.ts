/**
 * 同花顺认证工具
 * 用于管理认证信息，传递给本地Node服务
 */

// 不需要导入任何API

export interface ThsAuthInfo {
  cookie?: string;
  hexinV?: string;
}

class ThsAuthManager {
  private authInfo: ThsAuthInfo = {};
  private hexinV: string = "A28O4kNJW7QW4F-IAl6JffMn-Ih8FMkKXW7HLIHmC7bl04F2ieRThm04VyOS";
  private cookies: string =
    "other_uid=Ths_iwencai_Xuangu_v68ap0kujs4k4imrnerwxjr57famzzht; ta_random_userid=9g62316ohx; cid=6f268b4a71c618000fef492e86fa9d251750135156; u_ukey=A10702B8689642C6BE607730E11E6E4A; u_uver=1.0.0; u_dpass=%2BgiMil%2BParR5wbXu1a8jzRc1pPwmpkDb83UPAadA3ZSyUPxzcTS9eD50NCWsmpnNHi80LrSsTFH9a%2B6rtRvqGg%3D%3D; u_did=B4A540EBDC04441ABB567B7254531D6A; u_ttype=WEB; user=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; userid=*********; u_name=mo_*********; escapename=mo_*********; ticket=95c273bca2f5196be0a8deea1e2f7183; user_status=0; utk=41fa54a38c36279ea772cd59aaaa941e; Hm_lvt_57a0e026e5e0e1e90bcea3f298e48e74=**********; HMACCOUNT=B5BF20EEE2CB513A; Hm_lpvt_57a0e026e5e0e1e90bcea3f298e48e74=**********; THSSESSID=1474a7eb36ac60ed48cb52e227; v=A28O4kNJW7QW4F-IAl6JffMn-Ih8FMkKXW7HLIHmC7bl04F2ieRThm04VyOS";

  constructor() {
    // 从localStorage加载认证信息，如果没有则使用默认值
    const saved = localStorage.getItem("ths_auth_info");
    if (saved) {
      try {
        this.authInfo = JSON.parse(saved);
      } catch (error) {
        console.error("解析本地认证信息失败:", error);
        this.authInfo = {
          hexinV: this.hexinV,
          cookie: this.cookies,
        };
      }
    } else {
      this.authInfo = {
        hexinV: this.hexinV,
        cookie: this.cookies,
      };
    }
  }

  /**
   * 手动设置认证信息
   */
  async setAuthInfo(authInfo: ThsAuthInfo) {
    this.authInfo = { ...this.authInfo, ...authInfo };

    // 保存到localStorage
    this.saveToLocalStorage();

    console.log("🔑 设置认证信息:", {
      hexinV: authInfo.hexinV ? "已设置" : "未设置",
      cookie: authInfo.cookie ? "已设置" : "未设置",
    });
  }

  /**
   * 从抓包信息快速设置
   */
  async setFromPacket(packetInfo: { hexinV?: string; cookie?: string }) {
    if (packetInfo.hexinV) {
      this.authInfo.hexinV = packetInfo.hexinV;
    }

    if (packetInfo.cookie) {
      this.authInfo.cookie = packetInfo.cookie;
    }

    // 保存到localStorage
    this.saveToLocalStorage();

    console.log("📦 从抓包信息设置认证:", this.authInfo);
  }

  /**
   * 获取认证信息
   */
  getAuthInfo(): ThsAuthInfo {
    return { ...this.authInfo };
  }

  /**
   * 获取认证参数，用于传递给Node服务
   */
  getAuthParams(): { cookie: string; hexinV: string } {
    return {
      cookie: this.authInfo.cookie || "",
      hexinV: this.authInfo.hexinV || "",
    };
  }

  /**
   * 检查是否有有效的认证信息
   */
  hasValidAuth(): boolean {
    return !!(this.authInfo.hexinV && this.authInfo.cookie);
  }

  /**
   * 清除认证信息
   */
  clearAuth() {
    this.authInfo = {};
    localStorage.removeItem("ths_auth_info");
    console.log("🧹 清除认证信息");
  }

  /**
   * 提供设置指导
   */
  getSetupGuide(): string {
    return `
🔧 同花顺认证设置指南:

1. 打开浏览器开发者工具 (F12)
2. 访问同花顺智能选股页面
3. 在Network面板找到 get-robot-data 请求
4. 复制请求头中的信息:
   - hexin-v: AxgPHARDx...
   - Cookie: ta_random_userid=...

5. 将这些信息填入认证设置表单
6. 点击"确定"保存
    `;
  }

  /**
   * 保存认证信息到localStorage
   */
  private saveToLocalStorage() {
    try {
      localStorage.setItem("ths_auth_info", JSON.stringify(this.authInfo));
      console.log("💾 认证信息已保存到localStorage");
    } catch (error) {
      console.error("❌ 保存认证信息到localStorage失败:", error);
    }
  }

  /**
   * 从localStorage加载认证信息
   */
  private loadFromLocalStorage() {
    try {
      const saved = localStorage.getItem("ths_auth_info");
      if (saved) {
        this.authInfo = JSON.parse(saved);
        console.log("📥 从localStorage加载认证信息");
      }
    } catch (error) {
      console.error("❌ 从localStorage加载认证信息失败:", error);
    }
  }
}

export default new ThsAuthManager();
