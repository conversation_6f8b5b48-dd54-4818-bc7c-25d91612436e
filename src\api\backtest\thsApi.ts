import request from "@/utils/thsRequest";

// 请求参数类型
export interface ThslParams {
  question?: string;
  query?: string;
  perpage?: number;
  page?: number;
  source?: string;
  version?: string;
  secondary_intent?: string;
  rsh?: string;
  [key: string]: any;
}

// 认证参数类型
export interface ThslAuthParams {
  cookie: string;
  hexinV: string;
}

// 完整参数类型
export interface ThslFullParams {
  auth?: ThslAuthParams;
  params: string | ThslParams;
}

// API响应类型
export interface ThsApiResponse {
  status_code: number;
  status_msg: string;
  cost_time: number;
  result: {
    data: any[];
    total: number;
    page: number;
    perpage: number;
    components?: {
      show_type: string;
      data?: {
        datas: any[];
      };
    }[];
  };
  debug_info?: string;
}

// 股票数据类型
export interface StockData {
  code: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCode?: string; // 市场代码
  board?: string; // 上市板块
  macdInfo?: string; // MACD零轴金叉信息
  biddingPrice?: number; // 竞价匹配价
  biddingChange?: number; // 竞价涨幅
  biddingVolume?: number; // 竞价量
  biddingAmount?: number; // 竞价金额
  technicalPattern?: string; // 技术形态
  buySignal?: string; // 买入信号
  registeredAddress?: string; // 注册地址
  businessScope?: string; // 经营范围
  [key: string]: any;
}

// 查询响应类型
export interface QueryResponse {
  jobId: string;
}

// 进度响应类型
export interface ProgressResponse {
  progress: number;
}

// 查询结果项类型
export interface QueryResultItem {
  date: string;
  code: string;
  name: string;
  price: number;
  changePercent: number;
  volume: number;
  amount: number;
  details: string;
}

// 查询结果响应类型
export interface QueryResultsResponse {
  results: QueryResultItem[];
}

// 股票数据返回类型
export interface StockDataResponse {
  stocks: StockData[];
  total: number;
}

// 回测API类
class ThsApi {
  /**
   * 智能选股接口
   */
  async getRobotData(fullParams: ThslFullParams): Promise<ThsApiResponse> {
    return request({
      url: "/ths-api/thsl/search",
      method: "post",
      data: fullParams,
    });
  }

  /**
   * 解析股票数据
   */
  parseStockData(apiResponse: ThsApiResponse): StockData[] {
    try {
      if (apiResponse.status_code !== 0 || !apiResponse.result) {
        throw new Error(apiResponse.status_msg || "数据解析失败");
      }

      const { result } = apiResponse;
      const stocks: StockData[] = [];

      // 处理直接返回的数据数组
      if (result.data && Array.isArray(result.data)) {
        result.data.forEach((item: any) => {
          stocks.push({
            code: item.code || "",
            name: item.name || "",
            price: parseFloat(item.price) || 0,
            change: parseFloat(item.change) || 0,
            changePercent: parseFloat(item.changePercent) || 0,
            volume: parseInt(item.volume) || 0,
            ...item,
          });
        });
      }
      // 处理components结构
      else if (result.components && Array.isArray(result.components)) {
        const tableComponent = result.components.find(
          (component: any) => component.show_type === "xuangu_tableV1" || component.data?.datas
        );

        if (tableComponent && tableComponent.data?.datas) {
          const datas = tableComponent.data.datas;

          datas.forEach((stockItem: any) => {
            try {
              const stock: StockData = {
                code: stockItem["股票代码"] || stockItem["code"] || "",
                name: stockItem["股票简称"] || stockItem["name"] || "",
                price: parseFloat(stockItem["最新价"] || stockItem["price"] || "0"),
                change: parseFloat(stockItem["涨跌额"] || "0"),
                changePercent: parseFloat(stockItem["最新涨跌幅"] || stockItem["ratio"] || "0"),
                volume: parseInt(stockItem["成交量"] || stockItem["volume"] || "0"),
                ...stockItem,
              };

              stocks.push(stock);
            } catch (error) {
              console.error("解析股票数据项失败:", error);
            }
          });
        }
      }

      return stocks;
    } catch (error) {
      console.error("解析API响应数据失败:", error);
      return [];
    }
  }
}

// 导出类的实例
export default new ThsApi();

/**
 * 开始回测
 */
export function startBacktest(
  paramsArray: (string | ThslParams)[],
  auth?: Partial<ThslAuthParams>,
  startDate?: string,
  endDate?: string,
  enableMacdFilter?: boolean
) {
  return request<any, { data: { jobId: string } }>({
    url: "/ths-api/backtest/start",
    method: "post",
    data: {
      paramsArray,
      auth,
      startDate,
      endDate,
      enableMacdFilter,
    },
  });
}

/**
 * 获取回测进度
 */
export function getBacktestProgress(jobId: string) {
  return request<
    any,
    {
      code: number;
      data: {
        status: string;
        progress: {
          total: number;
          completed: number;
          failed: number;
          currentBatch: number;
        };
        updatedAt: string;
      };
    }
  >({
    url: `/ths-api/backtest/progress/${jobId}`,
    method: "get",
  });
}

/**
 * 获取回测结果
 */
export function getBacktestResults(jobId: string) {
  return request<
    any,
    {
      data: {
        total: number;
        results: QueryResultItem[];
      };
    }
  >({
    url: `/ths-api/backtest/results/${jobId}`,
    method: "get",
  });
}

/**
 * 轮询回测进度并获取结果
 * @param jobId 任务ID
 * @param onProgress 进度回调函数
 * @param onComplete 完成回调函数
 * @param onError 错误回调函数
 * @param interval 轮询间隔，默认3秒
 */
export function pollBacktestProgress(
  jobId: string,
  onProgress?: (progress: number) => void,
  onComplete?: (results: any[]) => void,
  onError?: (error: Error) => void,
  interval: number = 3000
): () => void {
  let isPolling = true;

  const poll = async () => {
    try {
      if (!isPolling) return;

      const progressResponse = await getBacktestProgress(jobId);
      const progress =
        progressResponse.data.progress.completed / progressResponse.data.progress.total;

      // 调用进度回调
      onProgress?.(progress);

      // 如果进度达到100%，获取结果并停止轮询
      if (progress >= 1) {
        isPolling = false;
        try {
          const results = await getBacktestResults(jobId);
          onComplete?.(results.data.results);
        } catch (resultsError) {
          onError?.(new Error(`获取结果失败: ${resultsError}`));
        }
        return;
      }

      // 继续轮询
      if (isPolling) {
        setTimeout(poll, interval);
      }
    } catch (error) {
      isPolling = false;
      onError?.(new Error(`轮询进度失败: ${error}`));
    }
  };

  // 开始轮询
  poll();

  // 返回停止函数
  return () => {
    isPolling = false;
  };
}

/**
 * 开始回测并轮询结果（一体化方法）
 * @param paramsArray 回测参数数组
 * @param auth 认证信息
 * @param onProgress 进度回调
 * @param onComplete 完成回调
 * @param onError 错误回调
 */
export async function startBacktestWithPolling(
  paramsArray: (string | ThslParams)[],
  auth?: Partial<ThslAuthParams>,
  onProgress?: (progress: number) => void,
  onComplete?: (results: any[]) => void,
  onError?: (error: Error) => void
): Promise<() => void> {
  try {
    // 开始回测
    const startResponse = await startBacktest(paramsArray, auth);
    const jobId = startResponse.data.jobId;

    if (!jobId) {
      throw new Error("未获取到任务ID");
    }

    console.log(`🚀 回测任务已开始，任务ID: ${jobId}`);

    // 开始轮询
    const stopPolling = pollBacktestProgress(jobId, onProgress, onComplete, onError);

    return stopPolling;
  } catch (error) {
    onError?.(new Error(`启动回测失败: ${error}`));
    return () => {};
  }
}
