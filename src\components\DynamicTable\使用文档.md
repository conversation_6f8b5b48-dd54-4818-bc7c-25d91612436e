# DynamicTable 动态表格组件使用文档

## 组件介绍

动态表格组件是一个支持以下功能的增强型表格组件：

1. 列显示/隐藏切换
2. 列顺序拖拽排序
3. 本地存储配置状态
4. 自定义列内容插槽
5. 响应式布局

## 安装与引入

直接页面中调用即可

## 组件属性 (Props)

| 属性名 | 类型 | 必填 | 默认值 | 说明 |
| --- | --- | --- | --- | --- |
| **data** | Array | ✔️ | - | 参考原 el-table 即可 |
| **columns** | Array | ✔️ | - | 列配置数组，详见[列配置结构](#列配置结构)说明 |
| **storageKey** | String | ✔️ | `'table'` | 本地存储配置的唯一标识键名<br>⚠️ 不同表格需使用不同键名避免配置冲突 |

### 列配置结构示例

```javascript
;[
  {
    prop: 'name', // 必填 - 对应数据字段的key
    label: '用户名', // 必填 - 列标题显示文本
    width: '120', // 可选 - 列宽（支持数字/字符串）
    sortable: true, // 可选 - 是否可排序
    slotName: 'custom' // 可选 - 自定义插槽名称（优先于prop匹配）
    // 可扩展其他el-table-column属性...
  }
]
```

🔑 storageKey 特别说明作用机制：通过localStorage持久化存储列配置状态命名规范：建议使用模块化命名（如：user-table、order-list）// 清除指定配置localStorage.removeItem('your-storage-key')

// 清除所有配置（谨慎使用）localStorage.clear()

### 实例代码

- 下面是案例，也可以参考**_房间管理模块_**的写法。路径是src/views/pw/room/index.vue比如其他el-table上有的属性，可以直接用在 DynamicTable 上面，组件内做了 $attrs 兼容。后续有问题可以反馈，在对组件做持续优化

<template>
  <DynamicTable
    :data="tableData"
    :columns="columns"
    storageKey="user-table"
    v-loading="loading"
    :stripe="true"
  >
    <template #operation="{ row }">
      <el-button @click="handleEdit(row)">编辑</el-button>
    </template>
  </DynamicTable>
</template>

<script setup>
const tableData = ref([
  { id: 1, name: '张三', age: 25, role: '管理员' },
  // 更多数据...
])

const columns = [
  { prop: 'id', label: 'ID', width: '80' },
  { prop: 'name', label: '用户名' },
  { prop: 'age', label: '年龄' },
  {
    prop: 'operation',
    label: '操作',
    slotName: 'operation'  // 指定使用operation插槽
  }
]
</script>

```

```
