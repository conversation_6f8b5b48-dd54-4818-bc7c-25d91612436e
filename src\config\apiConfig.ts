// API配置文件 - 区分mock和真实API
export interface ApiConfig {
  useMock: boolean;
  baseURL?: string;
  timeout?: number;
}

// 不同环境下的API配置
export const apiConfigs = {
  // Mock API配置（系统管理等接口）
  mock: {
    useMock: true,
    baseURL: import.meta.env.VITE_APP_BASE_API,
    timeout: 50000,
  },

  // 同花顺API配置（直接调用外部API）
  ths: {
    useMock: false,
    baseURL: "https://search.10jqka.com.cn",
    timeout: 30000,
  },

  // 其他第三方API配置
  external: {
    useMock: false,
    timeout: 30000,
  },
} as const;

// API类型枚举
export enum ApiType {
  MOCK = "mock", // 走mock系统的接口
  THS = "ths", // 同花顺接口
  EXTERNAL = "external", // 其他外部接口
}

// 根据API类型获取配置
export function getApiConfig(type: ApiType): ApiConfig {
  return apiConfigs[type];
}

// 判断是否使用mock
export function shouldUseMock(type: ApiType): boolean {
  return getApiConfig(type).useMock;
}
