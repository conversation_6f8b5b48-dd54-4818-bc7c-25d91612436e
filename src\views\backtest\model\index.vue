﻿<template>
  <div class="backtest-model-page">
    <el-card>
      <!-- 输入区域 -->
      <div class="input-section">
        <!-- 模型选择单独一行 -->
        <div class="model-row">
          <div class="input-group">
            <h3>选择模型</h3>
            <div class="model-select-row">
              <el-select v-model="currentModel" placeholder="选择模型类型" style="width: 300px">
                <el-option v-for="model in modelTypes" :key="model" :label="model" :value="model" />
              </el-select>
              <el-button type="danger" size="default" @click="showGuideDialog">操作指南</el-button>
            </div>
          </div>
        </div>

        <!-- 其他输入元素 -->
        <div class="input-row">
          <div class="input-group">
            <h3>请选择要查询的日期</h3>
            <div class="date-input-row">
              <el-date-picker
                v-model="selectedDate"
                type="date"
                placeholder="选择日期"
                format="YYYY年MM月DD日"
                value-format="YYYY年MM月DD日"
                :shortcuts="dateShortcuts"
                style="margin-right: 8px"
                popper-class="date-picker-dropdown"
                placement="bottom-start"
              />
              <el-input
                v-model="selectedDate"
                placeholder="如：2025年05月26日"
                style="width: 160px"
              />
            </div>
          </div>
          <div class="input-group">
            <h3>请输入要查询的板块</h3>
            <el-select v-model="block" placeholder="选择板块">
              <el-option
                v-for="option in blockOptions"
                :key="option"
                :label="option"
                :value="option"
              />
            </el-select>
          </div>
          <el-button type="danger" @click="handleCopyConditions" class="copy-btn">复制</el-button>
        </div>
      </div>

      <!-- 使用条件组件 -->
      <ModelConditions
        ref="modelConditionsRef"
        :selected-date="selectedDate"
        :block="block"
        :model-type="currentModel"
        @copy-conditions="handleCopySuccess"
      />
    </el-card>
    <!-- 操作指南弹窗 -->
    <el-dialog v-model="guideDialogVisible" title="操作指南" width="60%">
      <div class="guide-content">
        <h3>基本操作流程</h3>
        <ol>
          <li>选择模型类型（创业板龙虎榜、竞价涨幅模型、集合竞价量比模型）</li>
          <li>选择查询日期（可通过日期选择器或直接输入日期）</li>
          <li>选择要查询的板块（创业板、主板、科创板）</li>
          <li>根据需要添加条件（点击"添加条件"按钮）</li>
          <li>点击"复制"按钮复制查询条件</li>
        </ol>

        <h3>使用技巧</h3>
        <ul>
          <li>1. 把时间改为今日。去掉用于回测的参数，比如当日尾盘涨幅，次日尾盘涨幅这种字眼。</li>
          <li>2. 复制功能可以将条件复制到剪贴板，方便在其他地方使用</li>
          <li>3. 9点25分 打开 https://www.iwencai.com/unifiedwap/home/<USER>/li>
          <li>
            4.
            筛选的时候因为竞价量比这个数据是对比以往数据要查询的量比较大，故同花顺筛选不出来刚开始，大概在27，28分的时候可以筛选出来
            这个时候肉眼看一下，总市值这一栏选择市值大于40亿小于98亿的票，竞价异动类型为竞价抢筹。
            然后这个时候看一下竞价量比。只要10 - 40区间的。
          </li>
          <li>5. 买入,买入的时候要挂在2%左右，价格笼子是3%，不能挂这个很容易买入失败</li>
          <li>
            6.
            符合以上条件的票且昨日龙虎榜稳定前二的票是满分。市值大于98亿或者龙虎榜前四的票则是80分给2w即可。
          </li>
        </ul>
        <h3>疑问解答</h3>
        <ul>
          <li>1. 为什么不能把市值放到筛选条件里</li>
          <li>
            答：这个和龙虎榜排名有关，如果加了这个筛选条件会打乱龙虎榜排名关系，优先以龙虎榜排名为权重大
          </li>
          <li>2. 为什么不能把竞价异动类型放到筛选条件里</li>
          <li>答：这个和上面一样。</li>
          <li>3. 为什么不能把竞价量比放到筛选条件里</li>
          <li>
            答：竞价量比是放在筛选里的。但是需要注意9点25分的时候，是无法带出竞价量比的。9点27分左右才可以看到
          </li>
        </ul>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="danger" @click="guideDialogVisible = false">我知道了</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineAsyncComponent, watch } from "vue";
import { ElMessage } from "element-plus";
import type { ComponentPublicInstance } from "vue";
import { useBacktestStore } from "@/store";

// 动态导入组件来避免导入错误
const ModelConditions = defineAsyncComponent(() => import("./components/ModelConditions.vue"));

// 使用 Pinia store
const backtestStore = useBacktestStore();
const modelConditionsRef = ref<ComponentPublicInstance | null>(null);

// 从 store 获取响应式状态
const selectedDate = computed({
  get: () => backtestStore.selectedDate,
  set: (value: string) => backtestStore.setSelectedDate(value),
});

const block = computed({
  get: () => backtestStore.block,
  set: (value: string) => backtestStore.setBlock(value),
});

const currentModel = computed({
  get: () => backtestStore.currentModel,
  set: (value: string) => backtestStore.setCurrentModel(value),
});

// 模型类型
const modelTypes = ["创业板龙虎榜", "竞价涨幅模型", "集合竞价量比模型"];

// 选项数据
const blockOptions = ref(["创业板", "主板", "科创板"]);

// 日期快捷选项
const dateShortcuts = [
  {
    text: "今日",
    value: new Date(),
  },
  {
    text: "昨日",
    value: () => {
      const date = new Date();
      date.setTime(date.getTime() - 3600 * 1000 * 24);
      return date;
    },
  },
];

// 复制条件到剪贴板
const handleCopyConditions = async () => {
  if (modelConditionsRef.value) {
    await (modelConditionsRef.value as any).copyConditions();
  }
};

// 处理复制成功事件
const handleCopySuccess = (text: string) => {
  console.log("复制的内容:", text);
};

// 操作指南弹窗
const guideDialogVisible = ref(false);

// 显示操作指南弹窗
const showGuideDialog = () => {
  guideDialogVisible.value = true;
};
</script>

<style scoped>
.backtest-model-page {
  padding: 20px;
}

.input-section {
  background: #f9fafb;
  padding: 20px;
  border-radius: 8px;
  margin: 20px 0;
}

.model-row {
  margin-bottom: 20px;
}

.input-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.input-group {
  flex: 1;
  min-width: 160px;
}

.input-group h3 {
  font-size: 14px;
  margin-bottom: 8px;
  color: #333;
}

.date-input-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.model-select-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.copy-btn {
  align-self: flex-end;
  margin-top: 20px;
}

/* 强制日期选择器弹窗向下显示 */
:deep(.date-picker-dropdown) {
  position: absolute !important;
  top: 100% !important;
  left: 0 !important;
  right: auto !important;
  transform: none !important;
}

/* 全局设置所有日期选择器都向下弹 */
:deep(.el-picker-panel) {
  position: absolute !important;
}

/* 操作指南弹窗样式 */
.guide-content {
  line-height: 1.6;
  color: #333;
}

.guide-content h3 {
  margin-top: 20px;
  margin-bottom: 12px;
  color: #e70725; /* 大红色 */
  font-weight: bold;
  border-bottom: 1px solid #ffcdd2; /* 浅红色边框 */
  padding-bottom: 8px;
}

.guide-content h4 {
  margin-top: 16px;
  margin-bottom: 8px;
  color: #333;
  font-weight: bold;
}

.guide-content ol,
.guide-content ul {
  padding-left: 24px;
  margin-bottom: 16px;
}

.guide-content li {
  margin-bottom: 8px;
}

.guide-content p {
  margin-bottom: 12px;
}

.model-description {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
}

/* 自定义弹窗标题样式 */
:deep(.el-dialog__title) {
  color: #e70725 !important; /* 大红色标题 */
  font-weight: bold;
}

:deep(.el-dialog__header) {
  border-bottom: 2px solid #e70725; /* 大红色底边框 */
  padding-bottom: 15px;
}
</style>
