<template>
  <div class="stock-result-table">
    <el-card v-if="tableData.length > 0">
      <template #header>
        <div class="table-header">
          <div class="header-left">
            <span>查询结果 ({{ tableData.length }} 条)</span>
            <span class="total-profit" :class="getTotalProfitClass(totalProfit)">
              预计总收益: {{ formatTotalProfit(totalProfit) }}
            </span>
            <span class="total-profit" :class="getTotalProfitClass(totalOpenProfit)">
              预计次日开盘总收益: {{ formatTotalProfit(totalOpenProfit) }}
            </span>
          </div>
          <div class="header-right">
            <el-button type="danger" size="small" @click="exportToExcel">
              <i class="el-icon-download"></i>
              导出数据
            </el-button>
          </div>
        </div>
      </template>

      <DynamicTable
        v-loading="loading"
        :data="tableData"
        :columns="dynamicTableColumns"
        storage-key="thsl-stock-query-columns-config"
        stripe
        style="width: 100%"
        :scroll-x="true"
        :show-overflow-tooltip="true"
        fit
      >
        <!-- 为每个列创建插槽 -->
        <template v-for="col in tableColumns" :key="col.key" #[col.key]="scope">
          <span v-if="isNumberColumn(col.key)" :class="getValueClass(scope.row[col.key])">
            {{ formatColumnValue(scope.row[col.key], col.key) }}
          </span>
          <span v-else>
            {{ formatColumnValue(scope.row[col.key], col.key) }}
          </span>
        </template>
      </DynamicTable>
    </el-card>

    <el-empty v-else-if="!loading" description="暂无查询结果" />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import type { PropType } from "vue";
import {
  ElCard,
  ElTable,
  ElTableColumn,
  ElEmpty,
  ElMessage,
  ElButton,
  ElMessageBox,
} from "element-plus";
import { forIn } from "lodash-es";
import DynamicTable from "@/components/DynamicTable/index.vue";
import { exportAsCSV, exportAsHTML, exportTableData } from "@/utils/exportUtils";

// 接收原始 JSON 数据
const props = defineProps({
  data: {
    type: Object as PropType<any>,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  startDate: {
    // 添加开始日期属性
    type: String,
    default: "",
  },
  endDate: {
    // 添加结束日期属性
    type: String,
    default: "",
  },
});

const tableData = ref<Record<string, any>[]>([]);
const tableColumns = ref<{ key: string; label: string }[]>([]);
const totalProfit = ref<number>(0);
const totalOpenProfit = ref<number>(0); // 添加次日开盘总收益

// 转换为 DynamicTable 需要的列格式
const dynamicTableColumns = computed(() => {
  return tableColumns.value.map((col) => ({
    prop: col.key,
    label: col.label,
    width: getColumnWidth(col.key),
    minWidth: getMinColumnWidth(col.key),
    fixed: isFixedColumn(col.key),
    showOverflowTooltip: true,
    slotName: col.key,
  }));
});

// 从字段名中提取日期
function extractDateFromKey(key: string): string | null {
  // 处理带时间的格式 [YYYYMMDD HH:MM]
  const timeMatch = key.match(/\[(\d{8})\s+(\d{2}:\d{2})\]/);
  if (timeMatch) {
    return timeMatch[1]; // 只返回日期部分
  }

  // 处理标准日期格式 [YYYYMMDD]
  const dateMatch = key.match(/\[(\d{8})\]/);
  return dateMatch ? dateMatch[1] : null;
}

// 获取相对日期标签
function getRelativeLabel(fieldDate: string, baseDate: string): string | null {
  if (!fieldDate || !baseDate) return null;

  const fieldDateNum = parseInt(fieldDate);
  const baseDateNum = parseInt(baseDate);

  if (isNaN(fieldDateNum) || isNaN(baseDateNum)) return null;

  const diff = fieldDateNum - baseDateNum;
  if (diff === 0) return "当日";
  if (diff === -1) return "昨日";
  if (diff === 1) return "次日";
  return null;
}

// 解析并转换股票表格数据
function parseAndTransformStockTable(olddata: any) {
  // 1. 获取表头 这里要注意周六日的表头数据不能用
  // let results = {
  //   columns: olddata[0].data.answer[0].txt[0].content.components[0].data.columns;
  //   columns: olddata[0].data.answer[0].txt[0].content.components[0].data.columns;
  // }
  let results = olddata.map((el: any) => {
    return {
      columns: el?.data?.answer[0]?.txt[0]?.content?.components[0]?.data?.columns,
      datas: el?.data?.answer[0]?.txt[0]?.content?.components[0]?.data?.datas,
    };
  });
  let newColumns = null;

  // 找出锚点字段"竞价涨幅[日期]"

  const newData = {
    colums: [] as any[],
    datas: [] as any[],
  };

  const seenKeys = new Set();

  for (const block of results) {
    if (
      !block ||
      !Array.isArray(block.columns) ||
      block.columns.length === 0 ||
      !Array.isArray(block.datas)
    ) {
      // 跳过格式不对的 block
      continue;
    }

    // 找出锚点字段"竞价涨幅[日期]"
    const anchorCol = block.columns.find((col: any) => /^竞价涨幅\[\d{8}\]$/.test(col.key));

    if (!anchorCol) continue;

    const anchorDateStr = anchorCol.key.match(/\[(\d{8})\]/)[1];
    const anchorDate = parseInt(anchorDateStr);
    // 替换 key 中所有 [YYYYMMDD] 或 [YYYYMMDD HH:MM] 为相对日期标签
    const getRelativeKey = (key: any) => {
      if (!key) return key;

      // 处理带时间的格式 [YYYYMMDD HH:MM]
      return key
        .replace(/\[(\d{8})(\s+\d{2}:\d{2})\]/g, (_: any, dateStr: any, timeStr: any) => {
          const date = parseInt(dateStr);
          if (date === anchorDate) return `[当日${timeStr}]`;
          if (date < anchorDate) return `[昨日${timeStr}]`;
          if (date > anchorDate) return `[次日${timeStr}]`;
          return `[${dateStr}${timeStr}]`; // 非 ±1 天的保留原样
        })
        .replace(/\[(\d{8})\]/g, (_: any, dateStr: any) => {
          // 处理标准日期格式 [YYYYMMDD]
          const date = parseInt(dateStr);
          if (date === anchorDate) return "[当日]";
          if (date < anchorDate) return "[昨日]";
          if (date > anchorDate) return "[次日]";
          return `[${dateStr}]`; // 非 ±1 天的保留原样
        });
    };

    // 处理数据行
    for (const row of block.datas) {
      if (!row || typeof row !== "object") continue;
      const newRow = { 选入日期: anchorDate } as any;
      // 先处理原始字段
      for (const key in row) {
        const newKey = getRelativeKey(key);
        newRow[newKey] = row[key];
      }

      // 添加计算字段：预计盈利
      try {
        // 获取需要的值
        const 当日涨跌幅 = parseFloat(newRow["涨跌幅:前复权[当日]"] || "0");
        const 竞价涨幅 = parseFloat(newRow["竞价涨幅[当日]"] || "0");
        const 次日涨跌幅 = parseFloat(newRow["涨跌幅:前复权[次日]"] || "0");

        // 计算预计盈利 = 涨跌幅:前复权[当日] - (竞价涨幅[当日] + 2) + 涨跌幅:前复权[次日]
        const 预计盈利 = 当日涨跌幅 - (竞价涨幅 + 1) + 次日涨跌幅;

        // 添加到行数据 - 直接存储数值，不添加百分号
        newRow["预计盈利"] = isNaN(预计盈利) ? null : 预计盈利;

        // 计算次日开盘盈利 = 分时涨跌幅:前复权[次日 09:25] - (竞价涨幅[当日]+1) + 涨跌幅:前复权[当日]
        const 次日开盘分时涨跌幅 = parseFloat(newRow["分时涨跌幅:前复权[次日 09:25]"] || "0");
        const 次日开盘盈利 = 次日开盘分时涨跌幅 - (竞价涨幅 + 1) + 当日涨跌幅;

        // 添加到行数据
        newRow["次日开盘盈利"] = isNaN(次日开盘盈利) ? null : 次日开盘盈利;
      } catch (error) {
        console.warn("计算预计盈利失败:", error);
        newRow["预计盈利"] = null;
        newRow["次日开盘盈利"] = null;
      }

      newData.datas.push(newRow);
    }

    // 处理列定义
    for (const col of block.columns as any) {
      if (!col || !col.key) continue;
      const newKey = getRelativeKey(col.key);
      if (!seenKeys.has(newKey)) {
        const newCol = {
          ...col,
          key: newKey,
          label: newKey, // 使用处理后的key作为显示标签
          filter_keys: getRelativeKey(col.filter_keys || col.key),
          timestamp: newKey.includes("[当日]")
            ? "当日"
            : newKey.includes("[昨日]")
              ? "昨日"
              : newKey.includes("[次日]")
                ? "次日"
                : "",
        };
        newData.colums.push(newCol);
        seenKeys.add(newKey);
      }
    }
    // console.log(newData.colums);
    // debugger;
    // 添加预计盈利列定义 - 只在最后一个block处理完后添加
    if (
      block === results[results.length - 1] &&
      !newData.colums.find((c: any) => c.key === "预计盈利")
    ) {
      newData.colums.push({
        unit: "%",
        domain: "abs_计算字段",
        source: "calc",
        label: "预计盈利",
        type: "DOUBLE",
        index_name: "预计盈利",
        key: "预计盈利",
      });

      // 添加次日开盘盈利列定义
      newData.colums.push({
        unit: "%",
        domain: "abs_计算字段",
        source: "calc",
        label: "次日开盘盈利",
        type: "DOUBLE",
        index_name: "次日开盘盈利",
        key: "次日开盘盈利",
      });
    }
  }

  // 添加入选日期列，放最前面（如果不存在）
  if (!newData.colums.find((c: any) => c.key === "选入日期")) {
    newData.colums.unshift({
      unit: "",
      filter_value: "st",
      domain: "abs_选入日期",
      filter_oper: "不包含",
      source: "fixed_index",
      label: "选入日期",
      filter_keys: "选入日期",
      type: "STR",
      index_name: "选入日期",
      key: "选入日期",
    });
  }
  console.log(newData);

  // 定义筛选条件数组
  // 定义筛选条件
  const buySignalFilters = ["boll", "行情收盘价上穿5"];
  // const bidTypeFilters = ["竞价抢筹", "急速下跌", "竞价砸盘"];急速上涨 大买单试盘

  const bidTypeFilters = ["竞价抢筹"];
  // 这里是因为那里筛选的时候没法对这个条件进行筛选，还需要进一步筛查。垃圾问股不好用
  //// 目前不错的 25年  t0 竞价抢筹  t1 大幅高开    不赚钱这个 t2 急速下跌 , "急速上涨"
  //          24年  t0 竞价抢筹  t1 急速下跌     t2 大幅高开
  //          23年  t0 竞价抢筹  t1 急速下跌     t2 大幅高开
  console.log(`原始数据总数: ${newData.datas.length} 条`);

  // 第一步：先按照买入信号筛选
  const afterBuySignalFilter = newData.datas.filter((item) => {
    if (!item["买入信号inter[昨日]"] || typeof item["买入信号inter[昨日]"] !== "string") {
      return false;
    }
    const buySignal = item["买入信号inter[昨日]"].toLowerCase();
    return buySignalFilters.some((filter) => buySignal.includes(filter.toLowerCase()));
  });

  // 第二步：按照竞价异动类型筛选
  const afterBidTypeFilter = newData.datas.filter((item) => {
    if (!item["竞价异动类型[当日]"] || typeof item["竞价异动类型[当日]"] !== "string") {
      return false;
    }
    const bidType = item["竞价异动类型[当日]"].toLowerCase();
    return bidTypeFilters.some((filter) => bidType.includes(filter.toLowerCase()));
  });

  // 按照市值筛选（大于40亿）
  const afterMarketCapFilter = afterBidTypeFilter.filter((item) => {
    // 获取市值字段，可能是"总市值"或"总市值[当日]"等
    let marketCap = 0;
    const marketCapKeys = Object.keys(item).filter((key) => /总市值|流通市值/.test(key));

    // 尝试找到市值字段
    for (const key of marketCapKeys) {
      const value = parseFloat(item[key] || "0");
      if (!isNaN(value) && value > marketCap) {
        marketCap = value;
      }
    }

    // 市值单位处理 - 以元为单位判断
    // 40亿元 = 4,000,000,000元
    return marketCap >= 4000000000 && marketCap <= 9500000000;
    // return marketCap >= 4000000000;
  });

  // 打印筛选结果
  console.log(`市值大于40亿的数据: ${afterMarketCapFilter.length} 条`);

  // 按照选入日期筛选（大于2025年1月1日）
  const afterDateFilter = afterMarketCapFilter.filter((item) => {
    const 选入日期 = parseInt(item["选入日期"] || "0");
    // return 选入日期 >= 20240101 && 选入日期 <= 20250101;
    return 选入日期 >= 20250101;
  });

  // 打印筛选结果
  console.log(`选入日期大于2025年1月1日的数据: ${afterDateFilter.length} 条`);

  return {
    data: afterMarketCapFilter, // 返回按日期筛选后的数据
    columns: newData.colums,
  };
}

// 动态计算列宽
function getColumnWidth(key: string): number | undefined {
  // 基础列固定宽度
  if (key === "股票代码") return 120;
  if (key === "股票简称") return 120;
  if (key === "选入日期") return 120;

  // 其他列根据内容长度动态计算
  const baseWidth = 80;
  const charWidth = 14; // 每个字符大约14px
  const padding = 24; // 左右padding

  // 计算表头文字长度
  const headerLength = key.length;
  const calculatedWidth = headerLength * charWidth + padding;

  // 设置最小和最大宽度
  const minWidth = baseWidth;
  const maxWidth = 200;

  return Math.max(minWidth, Math.min(maxWidth, calculatedWidth));
}

// 获取最小列宽
function getMinColumnWidth(key: string): number {
  // 基础列固定最小宽度
  if (key === "股票代码") return 100;
  if (key === "股票简称") return 100;
  if (key === "选入日期") return 100;

  // 其他列动态最小宽度
  return Math.max(80, key.length * 12 + 20);
}

// 判断是否为固定列
function isFixedColumn(key: string): string | boolean | undefined {
  return ["股票代码", "股票简称", "入选日期"].includes(key) ? "left" : undefined;
}

// 判断是否为数值列
function isNumberColumn(key: string): boolean {
  return /涨跌幅|价格|价|量|金额|市值|比率|比例|强度|预计盈利|盈利/.test(key);
}

// 格式化列值
function formatColumnValue(value: any, key: string): string {
  if (value === undefined || value === null) return "--";

  // 处理数值类型
  if (isNumberColumn(key)) {
    const num = parseFloat(value);
    if (isNaN(num)) return value.toString();

    // 百分比类型
    if (/次日开盘盈利|涨跌幅|比率|比例|预计盈利/.test(key)) {
      return num.toFixed(2) + "%";
    }

    // 大数值格式化
    if (num >= 100000000) {
      return (num / 100000000).toFixed(2) + "亿";
    } else if (num >= 10000) {
      return (num / 10000).toFixed(2) + "万";
    }

    return num.toFixed(2);
  }

  return value.toString();
}

// 获取数值的样式类
function getValueClass(value: any): string {
  if (value === undefined || value === null) return "";

  const num = parseFloat(value);
  if (isNaN(num)) return "";

  return num > 0 ? "positive" : num < 0 ? "negative" : "";
}

// 计算总收益
function calculateTotalProfit(data: any[]) {
  if (!data || !data.length) return 0;

  // 计算所有行的预计盈利总和
  const sum = data.reduce((total, row) => {
    const profit = parseFloat(row["预计盈利"] || "0");
    return isNaN(profit) ? total : total + profit;
  }, 0);

  return sum;
}

// 计算次日开盘总收益
function calculateTotalOpenProfit(data: any[]) {
  if (!data || !data.length) return 0;

  // 计算所有行的次日开盘盈利总和
  const sum = data.reduce((total, row) => {
    const profit = parseFloat(row["次日开盘盈利"] || "0");
    return isNaN(profit) ? total : total + profit;
  }, 0);

  return sum;
}

// 格式化总收益
function formatTotalProfit(value: number): string {
  if (isNaN(value)) return "0.00%";
  return value.toFixed(2) + "%";
}

// 获取总收益的样式类
function getTotalProfitClass(value: number): string {
  if (isNaN(value) || value === 0) return "";
  return value > 0 ? "positive" : "negative";
}

// 导出功能
function exportToExcel() {
  try {
    // 使用导出工具准备数据
    const exportData = exportTableData(
      tableData.value,
      tableColumns.value,
      totalProfit.value,
      props.startDate,
      props.endDate,
      isNumberColumn,
      getColumnWidth
    );

    if (!exportData) return;

    const { columns, rows, dateStr, dataCount } = exportData;

    // 弹出选择导出格式的对话框
    ElMessageBox.confirm("请选择导出格式", "导出选项", {
      confirmButtonText: "CSV格式",
      cancelButtonText: "Excel格式(HTML)",
      distinguishCancelAndClose: true,
      type: "info",
    })
      .then(() => {
        // 用户选择了CSV格式
        exportAsCSV(columns, rows, dateStr, dataCount, totalProfit.value, isNumberColumn);
      })
      .catch((action) => {
        if (action === "cancel") {
          // 用户选择了Excel格式
          exportAsHTML(
            columns,
            rows,
            dateStr,
            dataCount,
            totalProfit.value,
            isNumberColumn,
            getColumnWidth
          );
        }
      });
  } catch (error) {
    console.error("导出失败:", error);
    ElMessage.error("导出失败: " + (error as Error).message);
  }
}

// 监听数据变化
watch(
  () => props.data,
  (newData) => {
    if (!newData) {
      tableData.value = [];
      tableColumns.value = [];
      return;
    }

    try {
      console.log(newData);
      const { data, columns } = parseAndTransformStockTable(newData) as any;
      console.log(data);
      console.log(columns);

      tableData.value = data;
      tableColumns.value = columns;
      console.log(tableColumns.value);
      if (data.length > 0) {
        ElMessage.success(`成功加载 ${data.length} 条股票数据`);
      }

      // 在下一个DOM更新周期添加表头title属性
      setTimeout(() => {
        const headerCells = document.querySelectorAll(".el-table .el-table__header th .cell");
        headerCells.forEach((cell: any) => {
          if (cell.textContent) {
            cell.setAttribute("title", cell.textContent.trim());
          }
        });
      }, 100);

      // 计算总收益
      totalProfit.value = calculateTotalProfit(data);
      // 计算次日开盘总收益
      totalOpenProfit.value = calculateTotalOpenProfit(data);
    } catch (error) {
      console.error("处理数据时出错:", error);
      ElMessage.error("数据处理失败");
      tableData.value = [];
      tableColumns.value = [];
    }
  },
  { immediate: true, deep: true }
);
</script>

<style scoped>
.stock-result-table {
  margin-top: 20px;
}

.table-header {
  font-weight: 600;
  font-size: 16px;
  color: #374151;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-right {
  display: flex;
  gap: 10px;
}

.total-profit {
  font-size: 14px;
  font-weight: bold;
  padding: 4px 12px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.positive {
  color: #f56c6c;
}

.negative {
  color: #67c23a;
}

/* 表头悬浮提示样式 - 使用纯CSS实现 */
:deep(.el-table .el-table__header th .cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  position: relative;
}

:deep(.el-table .el-table__header th .cell:hover::after) {
  content: attr(title);
  position: absolute;
  left: 0;
  top: 100%;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  z-index: 9999;
  white-space: normal;
  max-width: 300px;
  word-break: break-all;
}

/* 表格滚动条样式 - 强制移除Y轴滚动条 */
:deep(.el-table__body-wrapper) {
  overflow-x: auto;
  overflow-y: visible !important;
  max-height: none !important;
  height: auto !important;
}

:deep(.el-table__header-wrapper) {
  overflow-x: hidden;
}

/* 确保DynamicTable组件内的表格也没有Y轴滚动条 */
:deep(.dynamic-table .el-table) {
  height: auto !important;
  max-height: none !important;
}

:deep(.dynamic-table .el-table__body-wrapper) {
  overflow-y: visible !important;
  max-height: none !important;
  height: auto !important;
}

/* 确保表头和表体同步滚动和宽度对齐 */
:deep(.el-table__header) {
  width: 100% !important;
  table-layout: fixed;
}

:deep(.el-table__body) {
  width: 100% !important;
  table-layout: fixed;
}

/* 表格布局优化 - 允许动态调整 */
:deep(.el-table) {
  table-layout: auto;
  width: 100%;
}

:deep(.el-table th),
:deep(.el-table td) {
  box-sizing: border-box;
  text-align: center;
}

/* 数值列右对齐 */
:deep(.el-table td .cell) {
  text-align: center;
}

/* 文本列左对齐 */
:deep(.el-table th:first-child),
:deep(.el-table td:first-child),
:deep(.el-table th:nth-child(2)),
:deep(.el-table td:nth-child(2)),
:deep(.el-table th:nth-child(3)),
:deep(.el-table td:nth-child(3)) {
  text-align: left;
}

/* 同步滚动 */
:deep(.el-table__header-wrapper),
:deep(.el-table__body-wrapper) {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

:deep(.el-table__header-wrapper)::-webkit-scrollbar,
:deep(.el-table__body-wrapper)::-webkit-scrollbar {
  display: none;
}

/* 自定义滚动条样式 */
:deep(.el-table__body-wrapper)::-webkit-scrollbar {
  height: 8px;
}

:deep(.el-table__body-wrapper)::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper)::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper)::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
