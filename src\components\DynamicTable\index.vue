<template>
  <div class="dynamic-table">
    <div class="dynamic-table-crl">
      <el-button @click="handleOpenDrawer" type="primary" size="small" icon="filter">
        列设置
      </el-button>
    </div>

    <el-drawer title="选择显示的列" v-model="drawerVisible" direction="rtl" size="500px">
      <div v-if="workingDisplayedColumns.length" class="checkbox-container">
        <draggable
          v-model="workingDisplayedColumns"
          item-key="prop"
          handle=".drag-handle"
          @change="change"
          class="draggable-container"
        >
          <template #item="{ element }">
            <div class="checkbox-item">
              <el-checkbox
                :label="element.prop"
                v-model="element.isUse"
                class="full-width-checkbox"
              >
                {{ element.label }}
              </el-checkbox>

              <span class="drag-handle">≡≡</span>
            </div>
          </template>
        </draggable>
      </div>
      <el-empty v-else description="暂无可配置列" />

      <template #footer>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="applyColumnSettings">确认</el-button>
      </template>
    </el-drawer>

    <el-table
      :data="tableData"
      v-bind="$attrs"
      height="600px"
      max-height="70vh"
      :scrollbar-always-on="true"
      show-overflow-tooltip
    >
      <el-table-column v-for="column in displayedColumns" :key="column.prop" v-bind="column">
        <!-- 自动使用prop作为插槽名 -->
        <template #default="scope">
          <!-- 正确检测插槽是否存在 -->
          <template v-if="hasSlot(column.slotName || column.prop)">
            <slot :name="column.slotName || column.prop" v-bind="scope"></slot>
          </template>
          <!-- 默认内容回退 -->
          <!-- <template v-else>
          {{ scope.row[column.prop] }}
        </template> -->
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { defineProps, ref, computed, onMounted, onBeforeUnmount } from "vue";
import draggable from "vuedraggable";
import { useSlots } from "vue";

const slots = useSlots();

const hasSlot = (slotName) => {
  return !!slots[slotName];
};
const props = defineProps({
  data: { type: Array, required: true },
  columns: { type: Array, required: true },
  // 用来缓存table表头状态 这个必须按
  storageKey: { type: String, required: true, default: "table" },
});

// 状态管理（增加工作副本）
const drawerVisible = ref(false);
const workingDisplayedColumns = ref(
  [...props.columns].map((el) => {
    return {
      ...el,
      isUse: true,
    };
  })
);
// 实际生效的状态
const displayedColumns = ref([...props.columns]);
// 方法
const handleOpenDrawer = () => {
  // 初始化工作副本
  drawerVisible.value = true;
};
const handleCancel = () => {
  drawerVisible.value = false;
};
const applyColumnSettings = () => {
  // 提交工作副本到实际状态
  displayedColumns.value = [...workingDisplayedColumns.value].filter((el) => {
    return el.isUse;
  });
  drawerVisible.value = false;
};

// 响应式数据
const tableData = computed(() => props.data);
// 缓存相关
const saveState = () => {
  localStorage.setItem(props.storageKey, JSON.stringify(workingDisplayedColumns.value));
};

const loadState = () => {
  const oldColumns = JSON.parse(localStorage.getItem(props.storageKey));
  if (!oldColumns) return;
  console.log(oldColumns);
  const newTemp = props.columns
    .map((el) => {
      const oldTag = oldColumns.find((el2) => el2.label === el.label);
      return {
        ...el,
        isUse: typeof oldTag?.isUse === "boolean" ? oldTag.isUse : true,
      };
    })
    .sort((a, b) => {
      // 获取在旧列中的索引（未找到返回-1）
      const indexA = oldColumns.findIndex((col) => col.label === a.label);
      const indexB = oldColumns.findIndex((col) => col.label === b.label);

      // 转换为排序权重（未找到的排最后）
      const weightA = indexA === -1 ? Infinity : indexA;
      const weightB = indexB === -1 ? Infinity : indexB;

      return weightA - weightB;
    });
  workingDisplayedColumns.value = newTemp;
  applyColumnSettings();
  console.log(newTemp);
};

// 生命周期
onBeforeUnmount(() => {
  saveState(); // 页面卸载时保存状态
});

onMounted(() => {
  loadState(); // 组件挂载时加载状态
});
</script>

<!-- 样式保持不变 -->

<style scoped>
.checkbox-container {
  max-height: 90vh;
  overflow-y: auto;
}

.draggable-container {
  min-height: 60px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #ebeef5;
  gap: 12px;
  background: white;
  transition: background 0.2s;
}

.checkbox-item:hover {
  background: #f5f7fa;
}

.drag-handle {
  cursor: move;
  padding: 0 8px;
  color: #909399;
  user-select: none;
}

.full-width-checkbox {
  flex: 1;
  margin: 0;
}

.el-empty {
  padding: 32px 0;
}

.dynamic-table {
  width: 100%;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  background: #ffffff;
}

.dynamic-table-crl {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: end;
  padding: 16px 24px;
  background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
  border-bottom: 1px solid #e5e7eb;
}

/* 按钮美化 */
.dynamic-table-crl .el-button {
  border-radius: 8px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  transition: all 0.2s ease;
}

.dynamic-table-crl .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

/* 表格样式优化 - 现代化高档设计 */
:deep(.el-table) {
  font-size: 13px;
  border: none;
  background: #ffffff;
}

:deep(.el-table th) {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  color: #374151 !important;
  font-weight: 600;
  white-space: nowrap;
  padding: 18px 0;
  font-size: 14px;
  border-bottom: 2px solid #e2e8f0;
  border-right: none;
  border-left: none;
  position: relative;
}

:deep(.el-table th .cell) {
  color: #374151 !important;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 14px 0;
  border-bottom: 1px solid #f1f5f9;
  border-right: none;
  border-left: none;
  transition: all 0.2s ease;
  background: #ffffff;
}

:deep(.el-table .cell) {
  padding: 0 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}

/* 表格行悬停效果 - 高档渐变 */
:deep(.el-table tbody tr:hover) {
  background: linear-gradient(90deg, #f8fafc 0%, #f0f9ff 50%, #f8fafc 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

:deep(.el-table tbody tr:hover td) {
  background: transparent !important;
}

/* 斑马纹样式优化 */
:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #f9fafb;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped:hover) {
  background: linear-gradient(90deg, #f8fafc 0%, #f0f9ff 50%, #f8fafc 100%) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped:hover td) {
  background: transparent !important;
}

/* 表格滚动条样式 */
:deep(.el-table__body-wrapper) {
  overflow-x: auto;
  overflow-y: auto;
}

:deep(.el-table__header-wrapper) {
  overflow-x: hidden;
}

/* 表格布局优化 */
:deep(.el-table) {
  table-layout: auto;
  width: 100%;
}

:deep(.el-table th),
:deep(.el-table td) {
  box-sizing: border-box;
  text-align: center;
}

/* 数值列右对齐 */
:deep(.el-table td .cell) {
  text-align: center;
}

/* 文本列左对齐 */
:deep(.el-table th:first-child),
:deep(.el-table td:first-child),
:deep(.el-table th:nth-child(2)),
:deep(.el-table td:nth-child(2)),
:deep(.el-table th:nth-child(3)),
:deep(.el-table td:nth-child(3)) {
  text-align: left;
}
</style>
