import { defineStore } from "pinia";
import { ref } from "vue";
import { modelConfigs, type Condition } from "@/views/backtest/model/configs/modelConfigs";

// 回测模型的状态类型
export interface BacktestModelState {
  selectedDate: string;
  block: string;
  currentModel: string;
  modelConditions: Record<string, Condition[]>;
}

// 默认状态
const getDefaultState = (): BacktestModelState => ({
  selectedDate: "2025年05月26日",
  block: "创业板",
  currentModel: "创业板龙虎榜",
  modelConditions: {
    创业板龙虎榜: [],
    竞价涨幅模型: [],
    集合竞价量比模型: [],
  },
});

export const useBacktestStore = defineStore("backtest", () => {
  // 使用普通的 ref，不再使用 useStorage 缓存
  const state = ref<BacktestModelState>(getDefaultState());

  // 初始化模型条件（如果为空）
  const initializeModelConditions = () => {
    Object.keys(modelConfigs).forEach((modelType) => {
      if (
        !state.value.modelConditions[modelType] ||
        state.value.modelConditions[modelType].length === 0
      ) {
        state.value.modelConditions[modelType] = modelConfigs[modelType].defaultConditions(
          state.value.selectedDate
        );
      }
    });
  };

  // 设置选中的日期
  const setSelectedDate = (date: string) => {
    state.value.selectedDate = date;
    // 更新所有模型的条件中的日期
    updateAllModelConditions();
  };

  // 设置选中的板块
  const setBlock = (block: string) => {
    state.value.block = block;
    // 更新所有模型的条件中的板块
    updateAllModelConditions();
  };

  // 设置当前模型
  const setCurrentModel = (modelType: string) => {
    state.value.currentModel = modelType;
  };

  // 设置模型条件
  const setModelConditions = (modelType: string, conditions: Condition[]) => {
    state.value.modelConditions[modelType] = [...conditions];
  };

  // 获取指定模型的条件
  const getModelConditions = (modelType: string): Condition[] => {
    return state.value.modelConditions[modelType] || [];
  };

  // 获取当前模型的条件
  const getCurrentModelConditions = (): Condition[] => {
    return getModelConditions(state.value.currentModel);
  };

  // 更新所有模型的条件（当日期或板块变化时）
  const updateAllModelConditions = () => {
    Object.keys(state.value.modelConditions).forEach((modelType) => {
      const conditions = state.value.modelConditions[modelType];
      if (conditions && conditions.length > 0) {
        // 根据新的日期和板块更新条件文本
        const availableConditions = modelConfigs[modelType]?.availableConditions(
          state.value.selectedDate,
          state.value.block
        );

        if (availableConditions) {
          state.value.modelConditions[modelType] = conditions.map((condition) => {
            const updatedCondition = availableConditions.find((ac) => ac.id === condition.id);
            return updatedCondition || condition;
          });
        }
      }
    });
  };

  // 添加条件到指定模型
  const addConditionToModel = (modelType: string, condition: Condition) => {
    if (!state.value.modelConditions[modelType]) {
      state.value.modelConditions[modelType] = [];
    }

    // 检查条件是否已存在
    const exists = state.value.modelConditions[modelType].some((c) => c.id === condition.id);
    if (!exists) {
      state.value.modelConditions[modelType].push(condition);
    }
  };

  // 从指定模型移除条件
  const removeConditionFromModel = (modelType: string, index: number) => {
    if (state.value.modelConditions[modelType] && state.value.modelConditions[modelType][index]) {
      state.value.modelConditions[modelType].splice(index, 1);
    }
  };

  // 清空指定模型的条件
  const clearModelConditions = (modelType: string) => {
    state.value.modelConditions[modelType] = [];
  };

  // 重置所有状态
  const resetState = () => {
    const defaultState = getDefaultState();
    Object.assign(state.value, defaultState);
    initializeModelConditions();
  };

  // 获取可用的条件选项
  const getAvailableConditions = (modelType?: string) => {
    const targetModel = modelType || state.value.currentModel;
    const config = modelConfigs[targetModel];
    return config ? config.availableConditions(state.value.selectedDate, state.value.block) : [];
  };

  // 初始化
  initializeModelConditions();

  return {
    // 状态
    state: readonly(state),

    // Getters
    selectedDate: computed(() => state.value.selectedDate),
    block: computed(() => state.value.block),
    currentModel: computed(() => state.value.currentModel),
    modelConditions: computed(() => state.value.modelConditions),

    // Actions
    setSelectedDate,
    setBlock,
    setCurrentModel,
    setModelConditions,
    getModelConditions,
    getCurrentModelConditions,
    updateAllModelConditions,
    addConditionToModel,
    removeConditionFromModel,
    clearModelConditions,
    resetState,
    getAvailableConditions,
    initializeModelConditions,
  };
});

// 在组件外部使用的 Hook
export function useBacktestStoreHook() {
  return useBacktestStore();
}
