<template>
  <div>
    <el-card>
      <el-form ref="formRef" :model="queryForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="日期范围" prop="dateRange">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                format="YYYY-MM-DD"
                :shortcuts="dateRangeShortcuts"
                style="width: 100%"
                @change="handleDateRangeChange"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="查询条件" prop="query">
          <el-input
            v-model="queryForm.query"
            type="textarea"
            :rows="4"
            placeholder="请输入查询条件"
          />
        </el-form-item>

        <el-form-item label="启用MACD筛选">
          <el-switch
            v-model="queryForm.enableMacdFilter"
            active-color="#e70725"
            inactive-color="#dcdfe6"
          />
        </el-form-item>

        <el-form-item>
          <el-space>
            <el-button
              type="danger"
              :loading="loading"
              @click="handleQuery"
              class="start-query-btn"
            >
              开始查询
            </el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="info" @click="handleTestConnection">测试后端连接</el-button>
            <ths-auth />
            <el-button type="warning" @click="showNoticeDialog">注意事项</el-button>
          </el-space>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 进度显示 -->
    <div v-if="jobId" class="progress-section">
      <el-card>
        <el-progress
          :percentage="progress"
          :status="progressStatus"
          :stroke-width="15"
          color="#e70725"
        />
        <div class="status-text">{{ statusText }}</div>
        <!-- 当任务完成但没有结果时，显示手动获取结果按钮 -->
        <div v-if="progressStatus === 'success' && !hasResults" class="mt-3 text-center">
          <el-button type="danger" :loading="resultLoading" @click="manualGetResults">
            手动获取结果
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 注意事项弹窗 -->
    <el-dialog v-model="noticeDialogVisible" title="注意事项" width="50%">
      <div class="notice-content">
        <h3>数据筛选说明</h3>
        <ul>
          <li>
            目前回测数据拿到之后，这边手动筛选了市值大于40亿和竞价异动类型。其他的暂时没做筛选
          </li>

          <li>当前筛选条件: 市值大于40亿元 小于 95亿</li>
          <li>竞价异动类型: 竞价抢筹</li>
        </ul>

        <h3>收益计算方式</h3>
        <ul>
          <li>
            <strong>预计总收益:</strong>
            当日涨跌幅 + 次日涨跌幅 - 竞价涨幅 - 1%（买入成本）
          </li>
          <li>
            <strong>预计次日开盘总收益:</strong>
            当日涨跌幅 + 次日开盘涨跌幅 - 竞价涨幅 - 1%（买入成本）
          </li>
          <li>
            <strong>尾盘买入总收益:</strong>
            次日涨跌幅（尾盘买入无额外成本）
          </li>
        </ul>

        <h3>风险提示</h3>
        <p>历史数据仅供参考，不构成投资建议。投资有风险，入市需谨慎。</p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="noticeDialogVisible = false">我知道了</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onUnmounted, computed } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance } from "element-plus";
import ThsAuth from "@/components/ThsAuth/index.vue";
import thsAuth from "@/utils/thsAuth";
import thsApi from "@/api/backtest/thsApi";
import { createWorkdayParamsBuilder } from "@/utils/paramsTools";
import { startBacktest, getBacktestProgress, getBacktestResults } from "@/api/backtest/thsApi";
import type { QueryResultItem } from "@/api/backtest/thsApi";

defineOptions({
  name: "StockQueryForm",
  components: {
    ThsAuth,
  },
});

// 定义 Emits
interface Emits {
  (_e: "query-results", _results: QueryResultItem[], _startDate?: string, _endDate?: string): void;
  (_e: "query-reset"): void;
}

const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();
const dateRange = ref<[string, string]>(["", ""]);

const queryForm = ref({
  startDate: "",
  endDate: "",
  query: "",
  enableMacdFilter: false,
});

// 日期范围快捷选项
const dateRangeShortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近两周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 14);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

// 处理日期范围变化
const handleDateRangeChange = (value: [string, string] | null) => {
  if (value && value.length === 2) {
    queryForm.value.startDate = value[0];
    queryForm.value.endDate = value[1];
  } else {
    queryForm.value.startDate = "";
    queryForm.value.endDate = "";
  }
};

// 查询状态
const loading = ref(false);
const resultLoading = ref(false);
const jobId = ref("");
const progress = ref(0);
const progressStatus = ref<"" | "success" | "exception">("");
const statusText = ref("");
const hasResults = ref(false);

let progressTimer: number | null = null;

// 工具函数：生成日期数组
function getDatesArray(startDate: string, endDate: string): string[] {
  const dates: string[] = [];
  const currentDate = new Date(startDate);
  const end = new Date(endDate);

  while (currentDate <= end) {
    dates.push(currentDate.toISOString().split("T")[0]);
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return dates;
}

// 开始轮询进度
function startProgressPolling() {
  if (!jobId.value) return;

  stopProgressPolling();
  console.log(`开始轮询，任务ID: ${jobId.value}`);

  progressTimer = window.setInterval(async () => {
    try {
      const response = await getBacktestProgress(jobId.value);
      console.log(`轮询响应:`, response);

      // 更新进度 - 使用正确的数据结构
      const progressData = response.data.progress;
      const currentProgress =
        progressData.total > 0
          ? Math.round((progressData.completed / progressData.total) * 100)
          : 0;
      progress.value = currentProgress;

      // 根据状态处理 - 使用正确的数据结构
      if (response.data.status === "completed") {
        // 任务完成
        progressStatus.value = "success";
        statusText.value = "查询完成";
        stopProgressPolling();

        // 获取结果
        try {
          const resultsResponse = await getBacktestResults(jobId.value);
          hasResults.value = true;
          emit(
            "query-results",
            resultsResponse.data.results,
            queryForm.value.startDate,
            queryForm.value.endDate
          );
          ElMessage.success(`查询完成，共获得 ${resultsResponse.data.total} 条结果`);
        } catch {
          ElMessage.warning("任务完成但获取结果失败，请点击手动获取结果");
          statusText.value = "查询完成（结果获取失败）";
          hasResults.value = false;
        }
      } else if (response.data.status === "failed") {
        // 任务失败
        progressStatus.value = "exception";
        statusText.value = "查询失败";
        stopProgressPolling();
        ElMessage.error("查询任务失败");
      } else {
        // 任务进行中（processing 或其他状态）
        statusText.value = `正在查询 ${currentProgress}% (${progressData.completed}/${progressData.total})`;
        console.log(`任务进行中: ${response.data.status}, 进度: ${currentProgress}%`);
      }
    } catch (error) {
      console.error("轮询出错:", error);
      progressStatus.value = "exception";
      statusText.value = "查询失败";
      stopProgressPolling();
      ElMessage.error("获取进度失败");
    }
  }, 2000);
}

// 停止轮询进度
function stopProgressPolling() {
  if (progressTimer) {
    console.log("🛑 停止进度轮询");
    clearInterval(progressTimer);
    progressTimer = null;
  }
}

// 开始查询
const handleQuery = async () => {
  if (!queryForm.value.startDate || !queryForm.value.endDate) {
    ElMessage.warning("请选择开始和结束日期");
    return;
  }
  if (!queryForm.value.query.trim()) {
    ElMessage.warning("请输入查询条件");
    return;
  }

  loading.value = true;
  try {
    const dates = getDatesArray(queryForm.value.startDate, queryForm.value.endDate);

    // 使用高阶函数创建参数构建器，然后传入日期数组和查询字符串
    const buildParams = createWorkdayParamsBuilder();
    const paramsArray = buildParams(dates, queryForm.value.query);

    // 获取认证信息
    const authInfo = thsAuth.getAuthInfo();
    const auth = {
      cookie: authInfo.cookie,
      hexinV: authInfo.hexinV,
    };

    // 开始回测，传递MACD筛选参数
    const { data } = await startBacktest(
      paramsArray,
      auth,
      queryForm.value.startDate,
      queryForm.value.endDate,
      queryForm.value.enableMacdFilter
    );
    jobId.value = data.jobId;
    hasResults.value = false;
    startProgressPolling();
    ElMessage.success("查询任务已启动");
  } catch (error) {
    console.error("Failed to start query:", error);
    ElMessage.error("启动查询失败");
  } finally {
    loading.value = false;
  }
};

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields();
  stopProgressPolling();
  jobId.value = "";
  progress.value = 0;
  statusText.value = "";
  progressStatus.value = "";
  hasResults.value = false;
  emit("query-reset");
};

// 手动获取结果
const manualGetResults = async () => {
  if (!jobId.value) {
    ElMessage.warning("没有有效的任务ID");
    return;
  }

  resultLoading.value = true;
  try {
    console.log("🔄 手动获取查询结果...");
    const resultsResponse = await getBacktestResults(jobId.value);
    console.log(`📋 手动获得 ${resultsResponse.data.total} 条结果`);
    console.log("📋 结果数据:", resultsResponse.data.results);
    hasResults.value = true;
    emit(
      "query-results",
      resultsResponse.data.results,
      queryForm.value.startDate,
      queryForm.value.endDate
    );
    ElMessage.success(`成功获取 ${resultsResponse.data.total} 条结果`);
    statusText.value = "查询完成";
  } catch (error: any) {
    console.error("❌ 手动获取结果失败:", error);
    ElMessage.error(`获取结果失败: ${error.message || error}`);
  } finally {
    resultLoading.value = false;
  }
};

// 测试后端连接
const handleTestConnection = async () => {
  try {
    const authInfo = thsAuth.getAuthParams();
    const testParams = {
      params: {
        question: "创业板",
        query: "",
        perpage: 10,
        page: 1,
        source: "Ths_iwencai_Xuangu",
        version: "2.0",
        secondary_intent: "stock",
        rsh: "530032594",
      },
      auth: {
        cookie: authInfo.cookie,
        hexinV: authInfo.hexinV,
      },
    };

    console.log("开始测试后端连接...");
    console.log("测试参数:", testParams);

    // 调用真实的API接口
    const response = await thsApi.getRobotData(testParams);
    console.log("API响应:", response);

    ElMessage.success("后端连接正常，API调用成功");
  } catch (error: any) {
    console.error("后端连接测试失败:", error);
    ElMessage.error(`后端连接失败: ${error.message || error}`);
  }
};

// 组件卸载时清理定时器
onUnmounted(() => {
  stopProgressPolling();
});

// 注意事项弹窗
const noticeDialogVisible = ref(false);
const showNoticeDialog = () => {
  noticeDialogVisible.value = true;
};
</script>

<style scoped>
.progress-section {
  margin-top: 20px;
}

.status-text {
  margin-top: 10px;
  text-align: center;
  color: #666;
}

/* 覆盖进度条完成时的绿色图标，改为大红色 */
:deep(.el-progress--line .el-progress__text .el-icon) {
  color: #e70725 !important;
}

:deep(.el-progress.is-success .el-progress__text) {
  color: #e70725 !important;
}

:deep(.el-progress.is-success .el-progress-bar__outer .el-progress-bar__inner) {
  background-color: #e70725 !important;
}

/* 开始查询按钮自定义大红色 */
.start-query-btn {
  background-color: #e70725 !important;
  border-color: #e70725 !important;
}

.start-query-btn:hover {
  background-color: #d10622 !important;
  border-color: #d10622 !important;
}

.start-query-btn:active {
  background-color: #bd051f !important;
  border-color: #bd051f !important;
}

/* 注意事项弹窗样式 */
.notice-content {
  line-height: 1.6;
}

.notice-content h3 {
  margin-top: 16px;
  margin-bottom: 8px;
  color: #e70725;
  font-weight: bold;
}

.notice-content ul {
  padding-left: 20px;
}

.notice-content li {
  margin-bottom: 6px;
}

.notice-content p {
  margin-top: 8px;
  color: #666;
}
</style>
