<template>
  <div class="model-conditions">
    <!-- 条件展示区域 -->
    <div class="conditions-container">
      <div v-for="(condition, index) in conditions" :key="index" class="condition-item">
        <span class="condition-text">{{ condition.text }}</span>
        <el-button type="danger" size="small" @click="removeCondition(index)">删除</el-button>
      </div>
      <el-button type="danger" class="add-condition-btn" @click="showConditionSelector = true">
        添加条件
      </el-button>
    </div>

    <!-- 条件选择器弹窗 -->
    <div v-if="showConditionSelector" class="condition-selector-modal">
      <div class="modal-content">
        <h3>选择要添加的条件</h3>

        <!-- 全选选项 -->
        <div class="select-all-container">
          <label class="checkbox-label">
            <input type="checkbox" v-model="selectAll" @change="toggleSelectAll" />
            全选
          </label>
        </div>

        <!-- 条件列表 -->
        <div class="available-conditions">
          <div v-for="(option, index) in availableConditions" :key="index" class="condition-option">
            <label class="checkbox-label">
              <input type="checkbox" v-model="selectedConditions" :value="option" />
              {{ option.text }}
            </label>
          </div>
        </div>

        <!-- 按钮区域 -->
        <div class="modal-buttons">
          <el-button type="primary" @click="addSelectedConditions">确认</el-button>
          <el-button @click="showConditionSelector = false">取消</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { ElMessage } from "element-plus";
import { useBacktestStore } from "@/store";
import type { Condition } from "../configs/modelConfigs";

const props = defineProps({
  selectedDate: {
    type: String,
    required: true,
  },
  block: {
    type: String,
    required: true,
  },
  modelType: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(["copy-conditions"]);

// 使用 Pinia store
const backtestStore = useBacktestStore();

const showConditionSelector = ref(false);
const selectedConditions = ref<Condition[]>([]);
const selectAll = ref(false);

// 从 store 获取当前模型的条件
const conditions = computed(() => backtestStore.getModelConditions(props.modelType));

// 获取可用的条件选项
const availableConditions = computed(() => {
  return backtestStore.getAvailableConditions(props.modelType);
});

// 添加选中的条件
const addSelectedConditions = () => {
  console.log("确认选择的条件:", selectedConditions.value);

  // 清空当前模型的所有条件
  backtestStore.clearModelConditions(props.modelType);

  // 添加弹窗中选择的所有条件
  selectedConditions.value.forEach((condition) => {
    backtestStore.addConditionToModel(props.modelType, condition);
  });

  // 关闭弹窗
  showConditionSelector.value = false;

  // 显示成功消息
  ElMessage.success(`已设置 ${selectedConditions.value.length} 个条件`);
};

// 删除条件
const removeCondition = (index: number) => {
  console.log("删除条件索引:", index);
  backtestStore.removeConditionFromModel(props.modelType, index);
  ElMessage.success("条件删除成功");
};

// 复制条件到剪贴板
const copyConditions = async () => {
  try {
    const currentConditions = backtestStore.getModelConditions(props.modelType);
    const texts = currentConditions.map((condition) => condition.text);
    const combinedText = texts.join(";");
    await navigator.clipboard.writeText(combinedText);
    ElMessage.success("条件已复制到剪贴板");
    emit("copy-conditions", combinedText);
  } catch (error) {
    ElMessage.error("复制失败，请手动复制");
  }
};

// 全选/取消全选
const toggleSelectAll = () => {
  if (selectAll.value) {
    selectedConditions.value = [...availableConditions.value];
  } else {
    selectedConditions.value = [];
  }
};

// 打开弹窗时设置当前已选择的条件
watch(showConditionSelector, (isOpen) => {
  if (isOpen) {
    // 将当前页面上的条件设置为弹窗中的选中状态
    selectedConditions.value = [...conditions.value];
    // 检查是否全选
    selectAll.value = selectedConditions.value.length === availableConditions.value.length;
  }
});

// 暴露方法给父组件
defineExpose({
  copyConditions,
  conditions,
});
</script>

<style scoped>
.conditions-container {
  margin: 20px 0;
}

.condition-item {
  display: flex;
  align-items: center;
  margin: 5px 0;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  transition: all 0.2s ease;
}

.condition-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.condition-text {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.add-condition-btn {
  margin: 10px 0;
  padding: 8px 24px;
}

/* 模态框样式 */
.condition-selector-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 600px;
  max-width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-content h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  color: #333;
}

.select-all-container {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}

.available-conditions {
  max-height: none;
  overflow-y: visible;
}

.condition-option {
  margin: 8px 0;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.condition-option:hover {
  background: #f5f7fa;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
  margin-right: 8px;
  width: 16px;
  height: 16px;
}

.modal-buttons {
  margin-top: 24px;
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.modal-buttons .el-button {
  margin-left: 12px;
}
</style>
