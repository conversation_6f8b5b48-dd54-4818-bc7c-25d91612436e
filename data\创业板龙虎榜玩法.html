<!DOCTYPE html>
<html>

<head>
    <title>创业板龙虎榜玩法</title>
    <!-- 直接通过 CDN 引入 Vue 3.5 -->
    <script src="https://unpkg.com/vue@3.5.3/dist/vue.global.js"></script>
    <style>
        /* 全局样式 */
        body {
            font-family: Arial, sans-serif;
            margin: 20px auto;
        }

        .container {
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
        }

        .condition-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .condition-item button {
            margin-left: 10px;
            padding: 2px 8px;
            background: #ff4444;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        .add-condition {
            margin: 10px 0;
            padding: 5px 15px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <!-- 挂载点 -->
    <div id="app"></div>

    <script type="module">
        // 使用 Vue 3.5 的 Composition API + <script setup> 风格
        const { createApp, ref, computed, watch } = Vue
        // 去掉st;上市板块等于创业板;2024年4月1日市值大于25亿且小于400亿;2024年4月1日集合竞价量比大于10且小于40;2024年4月1日竞价涨幅大于0%;2024年4月1日收盘涨幅大于-20%;2024年4月1日后一个交易日开盘涨跌幅大于-20%;2024年4月1日后一个交易日收盘涨跌幅大于-20%;2024年4月1日前1个交易日买入信号;2024年4月1日竞价异动类型;2024年4月1日之前一个交易日的龙虎榜前三
        // 定义组件（直接写在 HTML 中）
        // 思路 看看能不能加入 板块龙头筛选 啥的
        const App = {
            template: `
            <div class="container">
                <h1>同花顺选股</h1>
                 <div style="margin-top: 20px; margin-bottom: 20px;">
                    参考日期 :
                    <div style="display: flex; align-items: center;">
                        <span v-for="option in copyDateOptions" :key="option">{{ option.text }}</span>
                    </div>
                </div>
                <div style="width: 80vw;display: flex; justify-content: space-between; align-items: center;">
                    <h2>请输入要查询的日期</h2>
                    <input v-model="date" />
                    <h2>请输入前一天的日期</h2>
                    <input v-model="date" />
                    <h2>请输入后一天的日期</h2>
                    <input v-model="date" />    
                    <h2>请输入要查询的板块</h2>
                    <select v-model="block">
                        <option v-for="option in blockOptions" :key="option">{{ option }}</option>
                    </select>      
                    <button @click="copy">复制</button>
                </div>
        
                <div ref="box">
                    <div v-for="(condition, index) in conditions" :key="index" class="condition-item">
                        <span>{{ condition.text }}</span>
                        <button @click="removeCondition(index)">删除</button>
                    </div>
                    <button class="add-condition" @click="showConditionSelector = true">添加条件</button>
                </div>

                <!-- 条件选择器弹窗 -->
                <div v-if="showConditionSelector" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border: 1px solid #ccc; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1);">
                    <h3>选择要添加的条件</h3>
                    <div style="margin-bottom: 10px;">
                        <label>
                            <input type="checkbox" v-model="selectAll" @change="toggleSelectAll">
                            全选
                        </label>
                    </div>
                    <div v-for="(option, index) in availableConditions" :key="index">
                        <label>
                            <input type="checkbox" v-model="selectedConditions" :value="option">
                            {{ option.text }}
                        </label>
                    </div>
                    <div style="margin-top: 20px; text-align: right;">
                        <button @click="addSelectedConditions" style="margin-right: 10px; padding: 5px 15px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer;">确认</button>
                        <button @click="showConditionSelector = false" style="padding: 5px 15px; background: #f44336; color: white; border: none; border-radius: 4px; cursor: pointer;">取消</button>
                    </div>
                </div>
            </div>
        `,
            setup() {
                const count = ref(0)
                const title = ref('单文件 Vue 3.5 演示')
                const parentMsg = '来自父组件的问候'
                const date = ref('2025年5月26日')
                const date2 = ref('2025年5月26日')

                const block = ref('创业板')
                const blockOptions = ref(['创业板', '主板', '科创板'])
                // 拿到 id 为box的div标签 下面所有span标签里的文字 然后进行复制
                const box = ref(null)
                const showConditionSelector = ref(false)
                const selectedConditions = ref([])
                const selectAll = ref(false)
                const copyDateOptions = ref([
                    { text: '2024年10月08日' },
                    { text: '2024年10月09日' },
                    { text: '2024年10月10日' }
                ])
                // 去掉st;上市板块等于创业板;当日市值大于25亿且小于400亿;当日集合竞价量比大于10且小于40;当日竞价涨幅大于0%;当日前1个交易日买入信号;当日竞价异动类型;当日之前一个交易日的龙虎榜前三;龙虎榜接连次数
                // 定义所有可用的条件
                const availableConditions = computed(() => [
                    { id: 'st', text: '去掉st' },
                    { id: 'block', text: `上市板块等于${block.value}` },
                    { id: 'market_value', text: `${date.value}市值大于25亿且小于400亿` },
                    // { id: 'open_price', text: `${date.value}9点25分成交量比值前3;` },
                    // { id: 'open_price2', text: `${date.value}集合竞价成交额大于500万;` },
                    { id: 'a', text: `${date.value}集合竞价量比大于10且小于40` },
                    { id: 'auction_change', text: `${date.value}竞价涨幅大于0%并且小于10.4%` },
                    // { id: 'auction_change2', text: `${date.value}竞价涨幅小于3%` },
                    { id: 'close_change', text: `${date.value}收盘涨幅大于-30%` },
                    { id: 'next_day_open', text: `${date.value}后一个交易日开盘涨跌幅大于-30%` },
                    { id: 'next_day_close', text: `${date.value}后一个交易日收盘涨跌幅大于-30%` },
                    { id: 'test_wm14', text: `${date.value}前1个交易日买入信号` },  // 2025年5月23日前5个交易日主力资金大于0
                    // { id: 'test_boll', text: `${date.value}前1个交易日买入信号为月线boll突破上轨或者${date.value}前1个交易日买入信号为月线boll突破中轨` },  // 2025年5月23日前5个交易日主力资金大于0行情收盘价上穿5日
                    // { id: 'test_boll2', text: `${date.value}前一个交易日不能涨停` },  // 2025年5月23日前5个交易日主力资金大于0行情收盘价上穿5日
                    // { id: 'auction_type', text: `${date.value}竞价异动类型为竞价抢筹` },
                    { id: 'auction_type', text: `${date.value}竞价异动类型` },
                    // { id: 'auction_type3', text: `${date.value}前一个交易日不能有炸板` },
                    // { id: 'auction_type4', text: `${date.value}前1个交易日的连续涨停天数<2` },
                    // { id: 'auction_type5', text: `${date.value}前4个交易日的涨停数量小于2或者没有涨停` },
                    // { id: 'c', text: `${date.value}前10个交易日有0轴附近macd金叉` },  // 2025年5月23日前5个交易日主力资金大于0
                    // { id: 'd', text: `${date.value}前1个交易日的macd环比增长值<0.03` },  // 2025年5月23日前5个交易日主力资金大于0
                    { id: 'auction_type6', text: `${date.value}之前一个交易日的龙虎榜前三` },
                    { id: 'auction_type6', text: `${date.value}之前一个交易日的龙虎榜前二` }, //这个胜率更高
                    // { id: 'auction_type7', text: `${date.value}之前一个交易日首板` },

                ])

                // 当前选中的条件
                const conditions = ref([
                    { id: 'st', text: '去掉st' },
                    { id: 'market_value', text: `${date.value}市值大于20亿且小于400亿` },
                ])

                // 监听选中条件的变化，实时更新conditions
                watch(selectedConditions, (newVal) => {
                    conditions.value = [...newVal]
                }, { deep: true })

                // 监听日期和板块变化，更新已选条件
                watch([date, date2, block], () => {
                    // 更新已选条件中的动态文本
                    conditions.value = conditions.value.map(condition => {
                        const availableCondition = availableConditions.value.find(ac => ac.id === condition.id)
                        return availableCondition || condition
                    })
                    // 同步更新selectedConditions
                    selectedConditions.value = [...conditions.value]
                })

                const addSelectedConditions = () => {
                    selectedConditions.value.forEach(condition => {
                        if (!conditions.value.some(c => c.id === condition.id)) {
                            conditions.value.push(condition)
                        }
                    })
                    showConditionSelector.value = false
                }

                const removeCondition = (index) => {
                    conditions.value.splice(index, 1)
                    // 同步更新selectedConditions
                    selectedConditions.value = [...conditions.value]
                }

                const copy = () => {
                    const texts = conditions.value.map(condition => condition.text)
                    const combinedText = texts.join(';')
                    navigator.clipboard.writeText(combinedText)
                }

                const toggleSelectAll = () => {
                    if (selectAll.value) {
                        selectedConditions.value = [...availableConditions.value]
                    } else {
                        selectedConditions.value = []
                    }
                }

                const increment = () => {
                    count.value++
                }

                // 定义子组件
                const ChildComp = {
                    props: ['msg'],
                    template: `
                    <div class="child">
                        <h3>子组件</h3>
                        <p>接收消息：{{ msg }}</p>
                        <p>随机色块：<span :style="{ background: randomColor }"></span></p>
                    </div>
                `,
                    setup(props) {
                        const randomColor = computed(() =>
                            `#${Math.floor(Math.random() * 16777215).toString(16).padStart(6, '0')}`
                        )
                        return { randomColor }
                    }
                }

                return { count, title, parentMsg, increment, ChildComp, copyDateOptions, date, date2, block, blockOptions, copy, box, conditions, availableConditions, showConditionSelector, selectedConditions, selectAll, addSelectedConditions, removeCondition, toggleSelectAll }
            }
        }

        // 创建并挂载应用
        createApp(App).mount('#app')
    </script>
</body>

</html>