// 访问 token 缓存的 key
const ACCESS_TOKEN_KEY = "access_token";

function getAccessToken(): string {
  return localStorage.getItem(ACCESS_TOKEN_KEY) || "";
}

function setAccessToken(token: string) {
  localStorage.setItem(ACCESS_TOKEN_KEY, token);
}

function clearToken() {
  localStorage.removeItem(ACCESS_TOKEN_KEY);
}

/**
 * 将字符串中的日期替换为指定日期
 * @param inputString 输入字符串
 * @param targetDate 目标日期 (YYYY-MM-DD格式)
 * @returns 替换后的字符串
 */
function replaceStringDates(inputString: string, targetDate: string): string {
  // 匹配各种日期格式的正则表达式
  const datePatterns = [
    // YYYY-MM-DD 格式
    /\d{4}-\d{1,2}-\d{1,2}/g,
    // YYYY/MM/DD 格式
    /\d{4}\/\d{1,2}\/\d{1,2}/g,
    // YYYY年MM月DD日 格式
    /\d{4}年\d{1,2}月\d{1,2}日/g,
    // MM-DD-YYYY 格式
    /\d{1,2}-\d{1,2}-\d{4}/g,
    // DD/MM/YYYY 格式
    /\d{1,2}\/\d{1,2}\/\d{4}/g,
  ];

  let result = inputString;

  // 依次应用所有正则表达式进行替换
  datePatterns.forEach((pattern) => {
    result = result.replace(pattern, targetDate);
  });

  return result;
}

/**
 * 将 YYYY-MM-DD 格式的日期转换为中文格式 YYYY年MM月DD日
 * @param dateString YYYY-MM-DD 格式的日期字符串
 * @returns 中文格式的日期字符串
 */
function formatDateToChinese(dateString: string): string {
  // 验证输入格式
  const dateRegex = /^\d{4}-\d{1,2}-\d{1,2}$/;
  if (!dateRegex.test(dateString)) {
    throw new Error("日期格式错误，请使用 YYYY-MM-DD 格式");
  }

  const [year, month, day] = dateString.split("-");

  // 确保月份和日期是两位数
  const formattedMonth = month.padStart(2, "0");
  const formattedDay = day.padStart(2, "0");

  return `${year}年${formattedMonth}月${formattedDay}日`;
}

/**
 * 过滤数组中的日期，只保留工作日（周一到周五），去除法定节假日
 * @param dateArray 日期字符串数组 (YYYY-MM-DD格式)
 * @returns 过滤后的工作日数组
 */
function filterWorkdays(dateArray: string[]): string[] {
  // 2022-2025年中国法定节假日列表
  const holidays = new Set([
    // 2022年节假日
    "2022-01-01",
    "2022-01-03", // 元旦
    "2022-01-31",
    "2022-02-01",
    "2022-02-02",
    "2022-02-03",
    "2022-02-04",
    "2022-02-05",
    "2022-02-06", // 春节
    "2022-04-03",
    "2022-04-04",
    "2022-04-05", // 清明节
    "2022-05-01",
    "2022-05-02",
    "2022-05-03",
    "2022-05-04", // 劳动节
    "2022-06-03",
    "2022-06-04",
    "2022-06-05", // 端午节
    "2022-09-10",
    "2022-09-11",
    "2022-09-12", // 中秋节
    "2022-10-01",
    "2022-10-02",
    "2022-10-03",
    "2022-10-04",
    "2022-10-05",
    "2022-10-06",
    "2022-10-07", // 国庆节

    // 2023年节假日
    "2023-01-01",
    "2023-01-02", // 元旦
    "2023-01-21",
    "2023-01-22",
    "2023-01-23",
    "2023-01-24",
    "2023-01-25",
    "2023-01-26",
    "2023-01-27", // 春节
    "2023-04-05", // 清明节
    "2023-05-01",
    "2023-05-02",
    "2023-05-03", // 劳动节
    "2023-06-22",
    "2023-06-23",
    "2023-06-24", // 端午节
    "2023-09-29",
    "2023-09-30", // 中秋节
    "2023-10-01",
    "2023-10-02",
    "2023-10-03",
    "2023-10-04",
    "2023-10-05",
    "2023-10-06", // 国庆节

    // 2024年节假日
    "2024-01-01", // 元旦
    "2024-02-10",
    "2024-02-11",
    "2024-02-12",
    "2024-02-13",
    "2024-02-14",
    "2024-02-15",
    "2024-02-16",
    "2024-02-17", // 春节
    "2024-04-04",
    "2024-04-05",
    "2024-04-06", // 清明节
    "2024-05-01",
    "2024-05-02",
    "2024-05-03",
    "2024-05-04",
    "2024-05-05", // 劳动节
    "2024-06-10", // 端午节
    "2024-09-15",
    "2024-09-16",
    "2024-09-17", // 中秋节
    "2024-10-01",
    "2024-10-02",
    "2024-10-03",
    "2024-10-04",
    "2024-10-05",
    "2024-10-06",
    "2024-10-07", // 国庆节

    // 2025年节假日
    "2025-01-01", // 元旦
    "2025-01-28",
    "2025-01-29",
    "2025-01-30",
    "2025-01-31",
    "2025-02-01",
    "2025-02-02",
    "2025-02-03",
    "2025-02-04", // 春节
    "2025-04-05",
    "2025-04-06",
    "2025-04-07", // 清明节
    "2025-05-01",
    "2025-05-02",
    "2025-05-03",
    "2025-05-04",
    "2025-05-05", // 劳动节
    "2025-05-31", // 端午节
    "2025-10-01",
    "2025-10-02",
    "2025-10-03",
    "2025-10-04",
    "2025-10-05",
    "2025-10-06",
    "2025-10-07", // 国庆节
    "2025-10-06", // 中秋节（与国庆节重叠）
  ]);

  return dateArray.filter((dateString) => {
    // 检查是否为法定节假日
    if (holidays.has(dateString)) {
      return false;
    }

    // 检查是否为周末
    const date = new Date(dateString);
    const dayOfWeek = date.getDay(); // 0 = 周日, 1 = 周一, ..., 6 = 周六

    // 只保留周一到周五 (1-5)
    return dayOfWeek >= 1 && dayOfWeek <= 5;
  });
}

/**
 * 高阶函数：创建带有日期处理和工作日过滤的参数数组构建器
 * @param config 配置对象，包含查询参数的各个字段
 * @returns 返回一个函数，该函数接收日期数组和查询字符串，返回处理后的参数数组
 */
function createWorkdayParamsBuilder(
  config = {
    page: 1,
    perpage: 50,
    query: "",
    rsh: "530032594",
    source: "Ths_iwencai_Xuangu",
    version: "2.0",
    secondary_intent: "stock",
  }
) {
  return (dates: string[], question: string) => {
    // 过滤掉法定节假日，只保留工作日
    const workdays = filterWorkdays(dates);

    // 构建查询参数数组，每个日期对应一个参数对象
    return workdays.map((date) => {
      // 将日期转换为中文格式
      const chineseDate = formatDateToChinese(date);
      // 替换查询字符串中的日期为真实日期
      const processedQuery = replaceStringDates(question, chineseDate);

      return {
        ...config,
        question: processedQuery,
        date, // 每个参数对象包含对应的日期
      };
    });
  };
}

export {
  getAccessToken,
  setAccessToken,
  clearToken,
  replaceStringDates,
  formatDateToChinese,
  filterWorkdays,
  createWorkdayParamsBuilder,
};
