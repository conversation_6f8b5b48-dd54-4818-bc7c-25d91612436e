<template>
  <div class="app-container">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="策略名称" prop="strategyName">
          <el-input
            v-model="queryParams.strategyName"
            placeholder="请输入策略名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="回测状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            style="width: 200px"
          >
            <el-option label="运行中" value="running" />
            <el-option label="已完成" value="completed" />
            <el-option label="已停止" value="stopped" />
            <el-option label="失败" value="failed" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <i-ep-search />
            搜索
          </el-button>
          <el-button @click="resetQuery">
            <i-ep-refresh />
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never">
      <template #header>
        <div class="flex justify-between">
          <div class="flex items-center space-x-2">
            <el-button type="primary" @click="openDialog">
              <i-ep-plus />
              新增回测
            </el-button>
            <el-button type="success" @click="openStockPickerDialog">
              <i-ep-search />
              智能选股
            </el-button>
            <ths-auth />
            <el-button type="danger" :disabled="ids.length === 0" @click="handleDelete()">
              <i-ep-delete />
              删除
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="backtestList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="ID" prop="id" width="80" align="center" />
        <el-table-column label="策略名称" prop="strategyName" min-width="150" />
        <el-table-column label="股票代码" prop="stockCode" width="120" align="center" />
        <el-table-column label="开始时间" prop="startDate" width="120" align="center" />
        <el-table-column label="结束时间" prop="endDate" width="120" align="center" />
        <el-table-column label="总收益率" prop="totalReturn" width="120" align="center">
          <template #default="scope">
            <span
              :class="{
                'text-green-500': scope.row.totalReturn > 0,
                'text-red-500': scope.row.totalReturn < 0,
              }"
            >
              {{ scope.row.totalReturn }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column label="年化收益率" prop="annualReturn" width="120" align="center">
          <template #default="scope">
            <span
              :class="{
                'text-green-500': scope.row.annualReturn > 0,
                'text-red-500': scope.row.annualReturn < 0,
              }"
            >
              {{ scope.row.annualReturn }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column label="最大回撤" prop="maxDrawdown" width="120" align="center">
          <template #default="scope">
            <span class="text-red-500">{{ scope.row.maxDrawdown }}%</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" width="180" align="center" />
        <el-table-column label="操作" fixed="right" width="220" align="center">
          <template #default="scope">
            <el-button type="primary" size="small" link @click="viewReport(scope.row)">
              <i-ep-view />
              查看报告
            </el-button>
            <el-button type="primary" size="small" link @click="openDialog(scope.row)">
              <i-ep-edit />
              编辑
            </el-button>
            <el-button type="danger" size="small" link @click="handleDelete(scope.row.id)">
              <i-ep-delete />
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>

    <!-- 回测表单弹窗 -->
    <el-dialog v-model="dialog.visible" :title="dialog.title" width="800px" @close="closeDialog">
      <el-form ref="backtestFormRef" :model="formData" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="策略名称" prop="strategyName">
              <el-input v-model="formData.strategyName" placeholder="请输入策略名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="股票代码" prop="stockCode">
              <el-input v-model="formData.stockCode" placeholder="请输入股票代码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startDate">
              <el-date-picker
                v-model="formData.startDate"
                type="date"
                placeholder="请选择开始时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endDate">
              <el-date-picker
                v-model="formData.endDate"
                type="date"
                placeholder="请选择结束时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="初始资金" prop="initialCapital">
              <el-input-number
                v-model="formData.initialCapital"
                :min="1000"
                :max="10000000"
                style="width: 100%"
                placeholder="请输入初始资金"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手续费率" prop="commission">
              <el-input-number
                v-model="formData.commission"
                :min="0"
                :max="1"
                :step="0.001"
                :precision="3"
                style="width: 100%"
                placeholder="请输入手续费率"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="策略描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入策略描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 智能选股弹窗 -->
    <el-dialog
      v-model="stockPickerDialog.visible"
      title="同花顺智能选股"
      width="1200px"
      @close="closeStockPickerDialog"
    >
      <el-form :inline="true" style="margin-bottom: 20px">
        <el-form-item label="选股条件">
          <el-input
            v-model="stockQuery"
            placeholder="请输入选股条件，如：十日内出现过零轴金叉;创业板;当日竞价涨幅大于2"
            style="width: 500px"
            @keyup.enter="handleStockQuery"
          />
        </el-form-item>
        <el-form-item label="每页条数">
          <el-input-number v-model="stockPerpage" :min="10" :max="100" style="width: 120px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="stockQueryLoading" @click="handleStockQuery">
            <i-ep-search />
            查询股票
          </el-button>
          <el-button @click="clearStockResults">
            <i-ep-refresh />
            清空结果
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 股票列表 -->
      <el-table
        v-loading="stockQueryLoading"
        :data="stockResults"
        max-height="400"
        @selection-change="handleStockSelection"
      >
        <el-table-column type="selection" width="50" align="center" />
        <el-table-column label="股票代码" prop="code" width="100" align="center" />
        <el-table-column label="股票名称" prop="name" width="120" />
        <el-table-column label="当前价格" prop="price" width="100" align="center">
          <template #default="scope">
            <span>{{ scope.row.price?.toFixed(2) || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="涨跌幅" prop="changePercent" width="100" align="center">
          <template #default="scope">
            <span
              :class="{
                'text-red-500': scope.row.changePercent > 0,
                'text-green-500': scope.row.changePercent < 0,
              }"
            >
              {{ scope.row.changePercent?.toFixed(2) || "-" }}%
            </span>
          </template>
        </el-table-column>
        <el-table-column label="上市板块" prop="board" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.board" type="info" size="small">
              {{ scope.row.board }}
            </el-tag>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="竞价涨幅" prop="biddingChange" width="100" align="center">
          <template #default="scope">
            <span
              v-if="scope.row.biddingChange"
              :class="{
                'text-red-500': scope.row.biddingChange > 0,
                'text-green-500': scope.row.biddingChange < 0,
              }"
            >
              {{ scope.row.biddingChange?.toFixed(2) }}%
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="竞价价格" prop="biddingPrice" width="100" align="center">
          <template #default="scope">
            <span>{{ scope.row.biddingPrice?.toFixed(2) || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="技术形态" prop="technicalPattern" width="150">
          <template #default="scope">
            <div v-if="scope.row.technicalPattern" class="text-sm">
              <el-tooltip :content="scope.row.technicalPattern" placement="top">
                <span class="text-blue-600 cursor-help">
                  {{ formatTechnicalPattern(scope.row.technicalPattern) }}
                </span>
              </el-tooltip>
            </div>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column label="买入信号" prop="buySignal" width="150">
          <template #default="scope">
            <div v-if="scope.row.buySignal" class="text-sm">
              <el-tooltip :content="scope.row.buySignal" placement="top">
                <span class="text-green-600 cursor-help">
                  {{ formatBuySignal(scope.row.buySignal) }}
                </span>
              </el-tooltip>
            </div>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column label="MACD信号" prop="macdInfo" width="120">
          <template #default="scope">
            <div v-if="scope.row.macdInfo" class="text-sm">
              <el-tooltip :content="scope.row.macdInfo" placement="top">
                <el-tag type="success" size="small">金叉</el-tag>
              </el-tooltip>
            </div>
            <span v-else class="text-gray-400">-</span>
          </template>
        </el-table-column>
        <el-table-column label="竞价量" prop="biddingVolume" width="100" align="center">
          <template #default="scope">
            <span>{{ formatVolume(scope.row.biddingVolume) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div v-if="stockResults.length > 0" class="flex justify-center mt-4">
        <el-pagination
          v-model:current-page="stockCurrentPage"
          :page-size="stockPerpage"
          :total="stockTotal"
          layout="prev, pager, next, jumper"
          @current-change="handleStockPageChange"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <span class="mr-4 text-sm text-gray-500">已选择 {{ selectedStocks.length }} 只股票</span>
          <el-button @click="closeStockPickerDialog">取消</el-button>
          <el-button
            type="primary"
            :disabled="selectedStocks.length === 0"
            @click="useSelectedStocks"
          >
            使用选中股票
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import type { FormInstance, FormRules } from "element-plus";
import thsApi, { type StockData } from "@/api/backtest/thsApi";
import thsAuth from "@/utils/thsAuth";
import ThsAuth from "@/components/ThsAuth/index.vue";

defineOptions({
  name: "MainBacktest",
  components: {
    ThsAuth,
  },
  inheritAttrs: false,
});

// 响应式数据
const queryFormRef = ref<FormInstance>();
const backtestFormRef = ref<FormInstance>();
const loading = ref(false);
const ids = ref<number[]>([]);
const total = ref(0);

// 查询参数
interface BacktestQueryParams {
  pageNum: number;
  pageSize: number;
  strategyName: string;
  status: string;
}

const queryParams = reactive<BacktestQueryParams>({
  pageNum: 1,
  pageSize: 10,
  strategyName: "",
  status: "",
});

// 回测列表数据
const backtestList = ref<any[]>([
  {
    id: 1,
    strategyName: "均线策略",
    stockCode: "000001",
    startDate: "2023-01-01",
    endDate: "2023-12-31",
    totalReturn: 15.6,
    annualReturn: 15.6,
    maxDrawdown: -8.2,
    status: "completed",
    createTime: "2024-01-15 10:30:00",
    initialCapital: 100000,
    commission: 0.001,
    description: "基于5日和20日均线的交易策略",
  },
  {
    id: 2,
    strategyName: "MACD策略",
    stockCode: "000002",
    startDate: "2023-01-01",
    endDate: "2023-12-31",
    totalReturn: -3.2,
    annualReturn: -3.2,
    maxDrawdown: -12.5,
    status: "running",
    createTime: "2024-01-16 14:20:00",
    initialCapital: 200000,
    commission: 0.001,
    description: "基于MACD指标的交易策略",
  },
]);

// 表单数据
const formData = reactive<{
  id?: number;
  strategyName: string;
  stockCode: string;
  startDate: string;
  endDate: string;
  initialCapital: number;
  commission: number;
  description: string;
}>({
  id: undefined,
  strategyName: "",
  stockCode: "",
  startDate: "",
  endDate: "",
  initialCapital: 100000,
  commission: 0.001,
  description: "",
});

// 对话框配置
const dialog = reactive({
  visible: false,
  title: "",
});

// 智能选股相关数据
const stockPickerDialog = reactive({
  visible: false,
});

// 智能选股查询参数
const stockQuery = ref("十日内出现过零轴金叉;创业板;当日竞价涨幅大于2");
const stockPerpage = ref(50);
const stockCurrentPage = ref(1);
const stockTotal = ref<number>(0);
const stockQueryLoading = ref(false);
const stockResults = ref<StockData[]>([]);
const selectedStocks = ref<StockData[]>([]);

// 表单验证规则
const rules: FormRules = {
  strategyName: [{ required: true, message: "请输入策略名称", trigger: "blur" }],
  stockCode: [{ required: true, message: "请输入股票代码", trigger: "blur" }],
  startDate: [{ required: true, message: "请选择开始时间", trigger: "change" }],
  endDate: [{ required: true, message: "请选择结束时间", trigger: "change" }],
  initialCapital: [{ required: true, message: "请输入初始资金", trigger: "blur" }],
  commission: [{ required: true, message: "请输入手续费率", trigger: "blur" }],
};

// 查询
const handleQuery = () => {
  loading.value = true;
  // 模拟API调用
  setTimeout(() => {
    loading.value = false;
    total.value = backtestList.value.length;
  }, 500);
};

// 重置查询
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  queryParams.pageNum = 1;
  handleQuery();
};

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  ids.value = selection.map((item) => item.id);
};

// 打开对话框
const openDialog = (row?: any) => {
  dialog.visible = true;
  if (row?.id) {
    dialog.title = "编辑回测";
    Object.assign(formData, row);
  } else {
    dialog.title = "新增回测";
    resetForm();
  }
};

// 关闭对话框
const closeDialog = () => {
  dialog.visible = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    id: undefined,
    strategyName: "",
    stockCode: "",
    startDate: "",
    endDate: "",
    initialCapital: 100000,
    commission: 0.001,
    description: "",
  });
  backtestFormRef.value?.resetFields();
};

// 提交表单
const handleSubmit = () => {
  backtestFormRef.value?.validate((valid) => {
    if (valid) {
      const loading = ElLoading.service({
        lock: true,
        text: "正在保存...",
      });

      // 模拟API调用
      setTimeout(() => {
        if (formData.id) {
          // 编辑
          const index = backtestList.value.findIndex((item) => item.id === formData.id);
          if (index !== -1) {
            backtestList.value[index] = {
              ...backtestList.value[index],
              ...formData,
              id: formData.id,
            };
          }
          ElMessage.success("编辑成功");
        } else {
          // 新增
          const newBacktest = {
            ...formData,
            id: Date.now(),
            totalReturn: 0,
            annualReturn: 0,
            maxDrawdown: 0,
            status: "running",
            createTime: new Date().toLocaleString(),
          };
          backtestList.value.unshift(newBacktest);
          ElMessage.success("新增成功");
        }
        loading.close();
        closeDialog();
        handleQuery();
      }, 1000);
    }
  });
};

// 删除
const handleDelete = (id?: number) => {
  const deleteIds = id ? [id] : ids.value;
  if (deleteIds.length === 0) {
    ElMessage.warning("请选择要删除的数据");
    return;
  }

  ElMessageBox.confirm(`确认删除已选中的${deleteIds.length}条数据项?`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    // 模拟API调用
    deleteIds.forEach((deleteId) => {
      const index = backtestList.value.findIndex((item) => item.id === deleteId);
      if (index !== -1) {
        backtestList.value.splice(index, 1);
      }
    });
    ElMessage.success("删除成功");
    handleQuery();
  });
};

// 查看报告
const viewReport = (_row: any) => {
  ElMessage.info("报告功能开发中...");
};

// 获取状态标签类型
const getStatusTagType = (
  status: string
): "success" | "warning" | "info" | "danger" | "primary" | undefined => {
  switch (status) {
    case "running":
      return "warning";
    case "completed":
      return "success";
    case "stopped":
      return "info";
    case "failed":
      return "danger";
    default:
      return undefined;
  }
};

// 获取状态文本
const getStatusText = (status: string): string => {
  switch (status) {
    case "running":
      return "运行中";
    case "completed":
      return "已完成";
    case "stopped":
      return "已停止";
    case "failed":
      return "失败";
    default:
      return status;
  }
};

// 智能选股相关方法
const openStockPickerDialog = () => {
  stockPickerDialog.visible = true;
  stockResults.value = [];
  selectedStocks.value = [];
  stockCurrentPage.value = 1;
  stockTotal.value = 0;
};

const closeStockPickerDialog = () => {
  stockPickerDialog.visible = false;
  stockResults.value = [];
  selectedStocks.value = [];
};

const clearStockResults = () => {
  stockResults.value = [];
  selectedStocks.value = [];
  stockTotal.value = 0;
  stockCurrentPage.value = 1;
};

const handleStockQuery = async () => {
  if (!stockQuery.value) {
    ElMessage.warning("请输入选股条件");
    return;
  }

  stockQueryLoading.value = true;
  try {
    const response = await thsApi.getRobotData({
      params: {
        question: stockQuery.value,
        page: 1,
        perpage: 100,
        source: "Ths_iwencai_Xuangu",
        version: "2.0",
        secondary_intent: "stock",
      },
      auth: {
        cookie: thsAuth.getAuthParams().cookie || "",
        hexinV: thsAuth.getAuthParams().hexinV || "",
      },
    });

    stockResults.value = thsApi.parseStockData(response);
    stockTotal.value = response.result.total;
  } catch (error: any) {
    ElMessage.error(error.message || "查询失败");
  } finally {
    stockQueryLoading.value = false;
  }
};

const handleStockPageChange = (page: number) => {
  stockCurrentPage.value = page;
  handleStockQuery();
};

const handleStockSelection = (selection: StockData[]) => {
  selectedStocks.value = selection;
};

const useSelectedStocks = () => {
  if (selectedStocks.value.length === 0) {
    ElMessage.warning("请选择股票");
    return;
  }

  formData.stockCode = selectedStocks.value.map((stock) => stock.code).join(",");
  closeStockPickerDialog();
};

// 工具函数
const formatVolume = (volume: number | undefined): string => {
  if (!volume) return "-";
  if (volume >= 100000000) {
    return (volume / 100000000).toFixed(2) + "亿";
  }
  if (volume >= 10000) {
    return (volume / 10000).toFixed(2) + "万";
  }
  return volume.toString();
};

const formatTechnicalPattern = (pattern: string): string => {
  if (!pattern) return "-";
  return pattern.length > 15 ? pattern.slice(0, 15) + "..." : pattern;
};

const formatBuySignal = (signal: string): string => {
  if (!signal) return "-";
  return signal.length > 15 ? signal.slice(0, 15) + "..." : signal;
};

// 页面加载时查询数据
onMounted(() => {
  handleQuery();
});
</script>

<style scoped>
.search-container {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}
</style>
