{"name": "vue3-element-template", "version": "2.0.1", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit & vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint:eslint": "eslint --cache \"src/**/*.{vue,ts}\" --fix", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache \"**/*.{css,scss,vue}\" --fix", "preinstall": "npx only-allow pnpm"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^12.8.2", "@wangeditor-next/editor": "^5.6.34", "@wangeditor-next/editor-for-vue": "^5.1.14", "animate.css": "^4.1.1", "axios": "^1.8.2", "default-passive-events": "^2.0.0", "echarts": "^5.6.0", "element-plus": "^2.9.6", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "vuedraggable": "^4.1.0", "path-browserify": "^1.0.1", "path-to-regexp": "^8.2.0", "pinia": "^3.0.1", "qs": "^6.14.0", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@eslint/js": "^9.22.0", "@iconify/utils": "^2.3.0", "@types/codemirror": "^5.60.15", "@types/lodash-es": "^4.17.12", "@types/node": "^22.13.10", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.3", "@types/qs": "^6.9.18", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-vue": "^10.0.0", "globals": "^15.15.0", "postcss": "^8.5.3", "postcss-html": "^1.8.0", "postcss-scss": "^4.0.9", "prettier": "^3.5.3", "sass": "^1.85.1", "stylelint": "^16.15.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^6.0.0", "stylelint-config-recommended": "^15.0.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-prettier": "^5.0.3", "terser": "^5.39.0", "typescript": "^5.8.2", "typescript-eslint": "^8.26.1", "unocss": "65.4.3", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.1", "vite-plugin-mock-dev-server": "^1.8.4", "vue-eslint-parser": "^10.1.1", "vue-tsc": "^2.2.8"}, "engines": {"node": ">=18.0.0"}, "repository": "https://gitee.com/youlaiorg/vue3-element-admin.git", "author": "有来开源组织", "license": "MIT", "packageManager": "pnpm@9.8.0+sha512.8e4c3550fb500e808dbc30bb0ce4dd1eb614e30b1c55245f211591ec2cdf9c611cabd34e1364b42f564bd54b3945ed0f49d61d1bbf2ec9bd74b866fcdc723276"}