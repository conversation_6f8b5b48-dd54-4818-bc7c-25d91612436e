import { ElMessage } from "element-plus";

/**
 * 格式化列值
 */
function formatColumnValue(
  value: any,
  key: string,
  isNumberColumn: (key: string) => boolean
): string {
  if (value === undefined || value === null) return "--";

  // 处理数值类型
  if (isNumberColumn(key)) {
    const num = parseFloat(value);
    if (isNaN(num)) return value.toString();

    // 百分比类型
    if (/次日开盘盈利|涨跌幅|比率|比例|预计盈利/.test(key)) {
      return num.toFixed(2) + "%";
    }

    // 大数值格式化
    if (num >= 100000000) {
      return (num / 100000000).toFixed(2) + "亿";
    } else if (num >= 10000) {
      return (num / 10000).toFixed(2) + "万";
    }

    return num.toFixed(2);
  }

  return value.toString();
}

/**
 * 格式化总收益
 */
function formatTotalProfit(value: number): string {
  if (isNaN(value)) return "0.00%";
  return value.toFixed(2) + "%";
}

/**
 * 导出为CSV格式
 */
export function exportAsCSV(
  columns: { key: string; label: string }[],
  rows: Record<string, any>[],
  dateStr: string,
  dataCount: number,
  totalProfit: number,
  isNumberColumn: (key: string) => boolean
): void {
  try {
    // 创建表头行
    let csvContent = columns.map((col) => `"${col.label}"`).join(",") + "\n";

    // 创建数据行
    rows.forEach((row) => {
      const rowData = columns
        .map((col) => {
          const value = formatColumnValue(row[col.key], col.key, isNumberColumn);
          return `"${value}"`;
        })
        .join(",");
      csvContent += rowData + "\n";
    });

    // 添加空行
    csvContent += "\n";

    // 添加统计信息
    csvContent += `"统计信息","总数据条数: ${dataCount}条","预计总收益: ${formatTotalProfit(totalProfit)}"\n`;

    // 创建Blob对象 (添加BOM使Excel正确识别UTF-8)
    const blob = new Blob(["\uFEFF" + csvContent], { type: "text/csv;charset=utf-8" });

    // 创建下载链接
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = `股票查询结果_${dateStr}_${dataCount}条.csv`;

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    ElMessage.success("导出CSV成功");
  } catch (error) {
    console.error("导出CSV失败:", error);
    ElMessage.error("导出CSV失败: " + (error as Error).message);
  }
}

/**
 * 导出为HTML格式 (Excel可以打开)
 */
export function exportAsHTML(
  columns: { key: string; label: string }[],
  rows: Record<string, any>[],
  dateStr: string,
  dataCount: number,
  totalProfit: number,
  isNumberColumn: (key: string) => boolean,
  getColumnWidth: (key: string) => number | undefined
): void {
  try {
    // 创建HTML表格内容
    let tableHtml = '<table border="1" style="border-collapse: collapse; width: 100%;">';

    // 添加标题行（包含数据条数信息）
    tableHtml += `<caption style="font-size: 16px; font-weight: bold; margin-bottom: 10px; text-align: left;">
      股票查询结果（共 ${dataCount} 条）
    </caption>`;

    // 添加表头
    tableHtml += '<thead><tr style="background-color: #f2f2f2; font-weight: bold;">';
    columns.forEach((col) => {
      // 根据列类型设置宽度
      let width = getColumnWidth(col.key) || 100;
      tableHtml += `<th style="padding: 8px; text-align: center; width: ${width}px;">${col.label}</th>`;
    });
    tableHtml += "</tr></thead>";

    // 添加数据行
    tableHtml += "<tbody>";
    rows.forEach((row, index) => {
      // 添加斑马纹
      const rowStyle =
        index % 2 === 0 ? "background-color: #ffffff;" : "background-color: #f9f9f9;";
      tableHtml += `<tr style="${rowStyle}">`;

      columns.forEach((col) => {
        const rawValue = row[col.key];
        const value = formatColumnValue(rawValue, col.key, isNumberColumn);

        // 设置单元格样式（包括颜色）
        let cellStyle = "padding: 8px;";

        // 数值列右对齐，文本列左对齐
        if (isNumberColumn(col.key)) {
          cellStyle += "text-align: right;";
        } else {
          cellStyle += "text-align: left;";
        }

        // 添加颜色样式
        if (isNumberColumn(col.key)) {
          const num = parseFloat(rawValue);
          if (!isNaN(num)) {
            if (num > 0) {
              cellStyle += "color: #f56c6c;"; // 红色（正值）
            } else if (num < 0) {
              cellStyle += "color: #67c23a;"; // 绿色（负值）
            }
          }
        }

        tableHtml += `<td style="${cellStyle}">${value}</td>`;
      });

      tableHtml += "</tr>";
    });

    // 添加空行
    tableHtml += `<tr style="height: 20px;"><td colspan="${columns.length}" style="border: none;"></td></tr>`;

    // 添加统计信息行
    tableHtml += `<tr style="font-weight: bold;">`;
    tableHtml += `<td style="padding: 8px; text-align: left;">统计信息</td>`;
    tableHtml += `<td style="padding: 8px; text-align: left;">总数据条数: ${dataCount}条</td>`;

    // 总收益单元格（添加颜色）
    const profitCellStyle =
      totalProfit > 0
        ? "padding: 8px; text-align: left; color: #f56c6c;"
        : totalProfit < 0
          ? "padding: 8px; text-align: left; color: #67c23a;"
          : "padding: 8px; text-align: left;";

    tableHtml += `<td style="${profitCellStyle}">预计总收益: ${formatTotalProfit(totalProfit)}</td>`;

    // 填充剩余单元格
    for (let i = 3; i < columns.length; i++) {
      tableHtml += '<td style="padding: 8px;"></td>';
    }

    tableHtml += "</tr>";
    tableHtml += "</tbody></table>";

    // 创建完整HTML文档
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>股票查询结果</title>
        <style>
          table { border-collapse: collapse; width: 100%; }
          th, td { border: 1px solid #ddd; padding: 8px; }
          th { background-color: #f2f2f2; }
          tr:nth-child(even) { background-color: #f9f9f9; }
          caption { font-size: 16px; font-weight: bold; margin-bottom: 10px; text-align: left; }
        </style>
      </head>
      <body>
        <h2>股票查询结果（${dateStr}）</h2>
        ${tableHtml}
      </body>
      </html>
    `;

    // 创建Blob对象
    const blob = new Blob([htmlContent], { type: "application/vnd.ms-excel;charset=utf-8" });

    // 创建下载链接
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = `股票查询结果_${dateStr}_${dataCount}条.xlsx`;

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    ElMessage.success("导出Excel成功");
  } catch (error) {
    console.error("导出Excel失败:", error);
    ElMessage.error("导出Excel失败: " + (error as Error).message);
  }
}

/**
 * 导出数据
 */
export function exportTableData(
  tableData: Record<string, any>[],
  tableColumns: { key: string; label: string }[],
  totalProfit: number,
  startDate: string,
  endDate: string,
  isNumberColumn: (key: string) => boolean,
  getColumnWidth: (key: string) => number | undefined
):
  | {
      columns: { key: string; label: string }[];
      rows: Record<string, any>[];
      dateStr: string;
      dataCount: number;
    }
  | undefined {
  try {
    // 检查数据是否存在
    if (!tableData || tableData.length === 0) {
      ElMessage.warning("没有可导出的数据");
      return undefined;
    }

    // 获取数据条数
    const dataCount = tableData.length;

    // 获取第一条数据的选入日期
    const firstEntryDate = tableData[0]?.选入日期;
    let dateStr = "";

    // 如果有开始和结束日期，优先使用它们
    if (startDate && endDate) {
      dateStr = `${startDate}_${endDate}`;
    }
    // 否则尝试使用第一条数据的选入日期
    else if (firstEntryDate) {
      // 将数字格式的日期转换为字符串格式 YYYYMMDD
      const dateString = String(firstEntryDate);
      if (dateString.length === 8) {
        const year = dateString.substring(0, 4);
        const month = dateString.substring(4, 6);
        const day = dateString.substring(6, 8);
        dateStr = `${year}-${month}-${day}`;
      }
    }
    // 如果都没有，使用当前日期
    else {
      dateStr = new Date().toISOString().slice(0, 10);
    }

    // 获取DynamicTable组件的引用
    const dynamicTableRef = document.querySelector(".dynamic-table");
    if (!dynamicTableRef) {
      ElMessage.warning("无法获取表格组件");
      return undefined;
    }

    // 获取表格中实际显示的列（从DOM中获取）
    const visibleHeaders = Array.from(
      dynamicTableRef.querySelectorAll(".el-table__header-wrapper th:not(.is-hidden)")
    )
      .map((th) => {
        const cellText = th.textContent?.trim() || "";
        return {
          label: cellText,
          key: tableColumns.find((col) => col.label === cellText)?.key || cellText,
        };
      })
      .filter((col) => col.key); // 过滤掉没有key的列

    // 如果无法获取可见列，则使用所有列
    const columns = visibleHeaders.length > 0 ? visibleHeaders : tableColumns;
    const rows = tableData;

    return { columns, rows, dateStr, dataCount };
  } catch (error) {
    console.error("准备导出数据失败:", error);
    ElMessage.error("准备导出数据失败: " + (error as Error).message);
    throw error;
  }
}
