import { defineMock } from "./base";
import fs from "fs";
import path from "path";

export default defineMock([
  // 读取date.json数据的API
  {
    url: "ths/load-date-json",
    method: ["GET"],
    body: () => {
      try {
        // 读取date.json文件
        const filePath = path.join(process.cwd(), "src/views/backtest/stock-query/date.json");
        const fileContent = fs.readFileSync(filePath, "utf-8");
        const jsonData = JSON.parse(fileContent);

        console.log("✅ 成功读取date.json文件");
        console.log("📊 数据结构:", typeof jsonData);
        console.log("📊 Results长度:", jsonData.data?.results?.length);

        return {
          code: "00000",
          message: "success",
          data: jsonData,
        };
      } catch (error) {
        console.error("❌ 读取date.json失败:", error);
        return {
          code: "50000",
          message: "读取文件失败",
          data: null,
        };
      }
    },
  },
]);
