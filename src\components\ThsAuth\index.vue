<template>
  <el-button type="warning" @click="openAuthDialog">
    <i-ep-key />
    认证信息
  </el-button>

  <el-dialog v-model="dialog.visible" :title="dialog.title" width="500px" @close="closeDialog">
    <el-form ref="authFormRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="Cookie" prop="cookie">
        <el-input
          v-model="formData.cookie"
          type="textarea"
          :rows="3"
          placeholder="请输入同花顺Cookie"
        />
      </el-form-item>
      <el-form-item label="HexinV" prop="hexinV">
        <el-input v-model="formData.hexinV" placeholder="请输入同花顺HexinV" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";
import thsAuth from "@/utils/thsAuth";

// 表单引用
const authFormRef = ref<FormInstance>();

// 表单数据
const formData = reactive({
  cookie: "",
  hexinV: "",
});

// 对话框配置
const dialog = reactive({
  visible: false,
  title: "同花顺认证信息",
});

// 表单验证规则
const rules: FormRules = {
  cookie: [{ required: true, message: "请输入同花顺Cookie", trigger: "blur" }],
  // hexinV: [{ required: true, message: "请输入同花顺HexinV", trigger: "blur" }],
};

// 打开对话框
const openAuthDialog = () => {
  // 从认证管理器加载当前保存的信息
  const authInfo = thsAuth.getAuthInfo();
  formData.cookie = authInfo.cookie || "";
  formData.hexinV = authInfo.hexinV || "";
  dialog.visible = true;
};

// 关闭对话框
const closeDialog = () => {
  dialog.visible = false;
  formData.cookie = "";
  formData.hexinV = "";
  authFormRef.value?.resetFields();
};

// 提交表单
const handleSubmit = () => {
  authFormRef.value?.validate(async (valid) => {
    if (valid) {
      try {
        await thsAuth.setAuthInfo({
          cookie: formData.cookie,
          hexinV: formData.hexinV,
        });
        ElMessage.success("认证信息保存成功");
        closeDialog();
      } catch (error: any) {
        ElMessage.error(error.message || "保存失败");
      }
    }
  });
};

defineExpose({
  openAuthDialog,
});
</script>

<script lang="ts">
export default {
  name: "ThsAuth",
};
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}
</style>
