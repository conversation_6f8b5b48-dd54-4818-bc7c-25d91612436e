let results = [
  {
    columns: [
      {
        unit: "",
        filter_value: "st",
        domain: "abs_股票领域",
        filter_oper: "不包含",
        source: "fixed_index",
        label: "name",
        filter_keys: "股票简称",
        type: "STR",
        index_name: "股票简称",
        key: "股票简称",
      },
      {
        unit: "%",
        filter_value: "4.8",
        filter_oper: "<",
        domain: "abs_股票领域",
        source: "new_parser",
        filter_keys: "竞价涨幅[20250617]",
        type: "DOUBLE",
        index_name: "竞价涨幅",
        key: "竞价涨幅[20250617]",
        timestamp: "20250617",
      },
      {
        unit: "%",
        domain: "abs_股票领域",
        source: "fixed_index",
        label: "ratio",
        type: "DOUBLE",
        index_name: "最新涨跌幅",
        key: "最新涨跌幅",
        timestamp: "",
      },
      {
        unit: "",
        subtype: "split",
        domain: "abs_股票领域",
        source: "new_parser",
        type: "STR",
        index_name: "买入信号inter",
        key: "买入信号inter[20250616]",
        timestamp: "20250616",
      },
      {
        unit: "元",
        domain: "abs_股票领域",
        source: "new_parser",
        type: "DOUBLE",
        index_name: "a股市值(不含限售股)",
        key: "a股市值(不含限售股)[20250618]",
        timestamp: "20250618",
      },
      {
        unit: "",
        domain: "abs_股票领域",
        indexId: "",
        source: "calc",
        type: "DOUBLE",
        index_name: "{(}行情收盘价{-}{(}10日均线{*}0.97{)}{)}",
        key: "{(}行情收盘价[20250616]{-}{(}10日均线[20250616]{*}0.97{)}{)}",
        timestamp: "20250616",
      },
    ],
    datas: [
      {
        股票简称: "东方中科",
        "竞价涨幅[20250617]": 2.669,
        "买入信号inter[20250616]": "rsi底背离",
        "a股市值(不含限售股)[20250618]": 1000000,
        最新涨跌幅: "-2.442528736",
        "{(}行情收盘价[20250616]{-}{(}10日均线[20250616]{*}0.97{)}{)}": 0.31877000000000066,
      },
      {
        股票简称: "新易盛",
        "竞价涨幅[20250617]": 2.669,
        "买入信号inter[20250616]": "rsi底背离",
        "a股市值(不含限售股)[20250618]": 1000000,
        最新涨跌幅: "-2.442528736",
        "{(}行情收盘价[20250616]{-}{(}10日均线[20250616]{*}0.97{)}{)}": 0.31877000000000066,
      },
    ],
  },
  {
    columns: [
      {
        unit: "",
        filter_value: "st",
        domain: "abs_股票领域",
        filter_oper: "不包含",
        source: "fixed_index",
        label: "name",
        filter_keys: "股票简称",
        type: "STR",
        index_name: "股票简称",
        key: "股票简称",
      },
      {
        unit: "%",
        filter_value: "4.8",
        filter_oper: "<",
        domain: "abs_股票领域",
        source: "new_parser",
        filter_keys: "竞价涨幅[20250415]",
        type: "DOUBLE",
        index_name: "竞价涨幅",
        key: "竞价涨幅[20250415]",
        timestamp: "20250415",
      },
      {
        unit: "%",
        domain: "abs_股票领域",
        source: "fixed_index",
        label: "ratio",
        type: "DOUBLE",
        index_name: "最新涨跌幅",
        key: "最新涨跌幅",
        timestamp: "",
      },
      {
        unit: "",
        subtype: "split",
        domain: "abs_股票领域",
        source: "new_parser",
        type: "STR",
        index_name: "买入信号inter",
        key: "买入信号inter[20250414]",
        timestamp: "20250414",
      },
      {
        unit: "元",
        domain: "abs_股票领域",
        source: "new_parser",
        type: "DOUBLE",
        index_name: "a股市值(不含限售股)",
        key: "a股市值(不含限售股)[20250416]",
        timestamp: "20250416",
      },
      {
        unit: "",
        domain: "abs_股票领域",
        indexId: "",
        source: "calc",
        type: "DOUBLE",
        index_name: "{(}行情收盘价{-}{(}10日均线{*}0.97{)}{)}",
        key: "{(}行情收盘价[20250414]{-}{(}10日均线[20250414]{*}0.97{)}{)}",
        timestamp: "20250414",
      },
    ],
    datas: [
      {
        股票简称: "麦格米特",
        "竞价涨幅[20250415]": 2.669,
        "买入信号inter[20250414]": "rsi底背离",
        "a股市值(不含限售股)[20250416]": 1000000,
        最新涨跌幅: "-2.442528736",
        "{(}行情收盘价[20250414]{-}{(}10日均线[20250414]{*}0.97{)}{)}": 0.31877000000000066,
      },
      {
        股票简称: "新易盛",
        "竞价涨幅[20250415]": 2.669,
        "买入信号inter[20250414]": "rsi底背离",
        "a股市值(不含限售股)[20250416]": 1000000,
        最新涨跌幅: "-2.442528736",
        "{(}行情收盘价[20250414]{-}{(}10日均线[20250414]{*}0.97{)}{)}": 0.31877000000000066,
      },
    ],
  },
];

let newData = {
  colums: [
    {
      unit: "",
      filter_value: "st",
      domain: "abs_选入日期",
      filter_oper: "不包含",
      source: "fixed_index",
      label: "name",
      filter_keys: "选入日期",
      type: "STR",
      index_name: "选入日期",
      key: "选入日期",
    },
    {
      unit: "",
      filter_value: "st",
      domain: "abs_股票领域",
      filter_oper: "不包含",
      source: "fixed_index",
      label: "name",
      filter_keys: "股票简称",
      type: "STR",
      index_name: "股票简称",
      key: "股票简称",
    },
    {
      unit: "%",
      filter_value: "4.8",
      filter_oper: "<",
      domain: "abs_股票领域",
      source: "new_parser",
      filter_keys: "竞价涨幅[当日]",
      type: "DOUBLE",
      index_name: "竞价涨幅",
      key: "竞价涨幅[当日]",
      timestamp: "当日",
    },
    {
      unit: "%",
      domain: "abs_股票领域",
      source: "fixed_index",
      label: "ratio",
      type: "DOUBLE",
      index_name: "最新涨跌幅",
      key: "最新涨跌幅",
      timestamp: "",
    },
    {
      unit: "",
      subtype: "split",
      domain: "abs_股票领域",
      source: "new_parser",
      type: "STR",
      index_name: "买入信号inter",
      key: "买入信号inter[昨日]",
      timestamp: "昨日",
    },
    {
      unit: "元",
      domain: "abs_股票领域",
      source: "new_parser",
      type: "DOUBLE",
      index_name: "a股市值(不含限售股)",
      key: "a股市值(不含限售股)[次日]",
      timestamp: "次日",
    },
    {
      unit: "",
      domain: "abs_股票领域",
      indexId: "",
      source: "calc",
      type: "DOUBLE",
      index_name: "{(}行情收盘价{-}{(}10日均线{*}0.97{)}{)}",
      key: "{(}行情收盘价[昨日]{-}{(}10日均线[昨日]{*}0.97{)}{)}",
      timestamp: "昨日",
    },
  ],
  datas: [
    {
      入选日期: 20250617,
      股票简称: "东方中科",
      "竞价涨幅[当日]": 2.669,
      "买入信号inter[昨日]": "rsi底背离",
      "a股市值(不含限售股)[次日]": 1000000,
      最新涨跌幅: "-2.442528736",
      "{(}行情收盘价[昨日]{-}{(}10日均线[昨日]{*}0.97{)}{)}": 0.31877000000000066,
    },
    {
      入选日期: 20250617,
      股票简称: "新易盛",
      "竞价涨幅[当日]": 2.669,
      "买入信号inter[昨日]": "rsi底背离",
      "a股市值(不含限售股)[次日]": 1000000,
      最新涨跌幅: "-2.442528736",
      "{(}行情收盘价[昨日]{-}{(}10日均线[昨日]{*}0.97{)}{)}": 0.31877000000000066,
    },
    {
      入选日期: 20250415,
      股票简称: "麦格米特",
      "竞价涨幅[当日]": 2.669,
      "买入信号inter[昨日]": "rsi底背离",
      "a股市值(不含限售股)[次日]": 1000000,
      最新涨跌幅: "-2.442528736",
      "{(}行情收盘价[昨日]{-}{(}10日均线[昨日]{*}0.97{)}{)}": 0.31877000000000066,
    },
    {
      入选日期: 20250415,
      股票简称: "新易盛",
      "竞价涨幅[当日]": 2.669,
      "买入信号inter[昨日]": "rsi底背离",
      "a股市值(不含限售股)[次日]": 1000000,
      最新涨跌幅: "-2.442528736",
      "{(}行情收盘价[昨日]{-}{(}10日均线[昨日]{*}0.97{)}{)}": 0.31877000000000066,
    },
  ],
};
function transformResultsToNewData(results) {
  if (!Array.isArray(results) || results.length === 0) {
    // 输入不是数组或空数组，直接返回空结构
    return {
      colums: [
        {
          unit: "",
          filter_value: "st",
          domain: "abs_选入日期",
          filter_oper: "不包含",
          source: "fixed_index",
          label: "name",
          filter_keys: "选入日期",
          type: "STR",
          index_name: "选入日期",
          key: "选入日期",
        },
      ],
      datas: [],
    };
  }

  const newData = {
    colums: [],
    datas: [],
  };

  const seenKeys = new Set();

  for (const block of results) {
    if (
      !block ||
      !Array.isArray(block.columns) ||
      block.columns.length === 0 ||
      !Array.isArray(block.datas)
    ) {
      // 跳过格式不对的 block
      continue;
    }

    // 找出锚点字段“竞价涨幅[日期]”
    const anchorCol = block.columns.find((col) => /^竞价涨幅\[\d{8}\]$/.test(col.key));
    if (!anchorCol) continue;

    const anchorDateStr = anchorCol.key.match(/\[(\d{8})\]/)[1];
    const anchorDate = parseInt(anchorDateStr);

    // 替换 key 中所有 [YYYYMMDD] 为相对日期标签
    const getRelativeKey = (key) => {
      if (!key) return key;
      return key.replace(/\[(\d{8})\]/g, (_, dateStr) => {
        const date = parseInt(dateStr);
        if (date === anchorDate) return "[当日]";
        if (date === anchorDate - 1) return "[昨日]";
        if (date === anchorDate + 1) return "[次日]";
        return `[${dateStr}]`; // 非 ±1 天的保留原样
      });
    };

    // 处理数据行
    for (const row of block.datas) {
      if (!row || typeof row !== "object") continue;
      const newRow = { 入选日期: anchorDate };
      for (const key in row) {
        const newKey = getRelativeKey(key);
        newRow[newKey] = row[key];
      }
      newData.datas.push(newRow);
    }

    // 处理列定义
    for (const col of block.columns) {
      if (!col || !col.key) continue;
      const newKey = getRelativeKey(col.key);
      if (!seenKeys.has(newKey)) {
        const newCol = {
          ...col,
          key: newKey,
          filter_keys: getRelativeKey(col.filter_keys || col.key),
          timestamp: newKey.includes("[当日]")
            ? "当日"
            : newKey.includes("[昨日]")
              ? "昨日"
              : newKey.includes("[次日]")
                ? "次日"
                : "",
        };
        newData.colums.push(newCol);
        seenKeys.add(newKey);
      }
    }
  }

  // 添加入选日期列，放最前面（如果不存在）
  if (!newData.colums.find((c) => c.key === "选入日期")) {
    newData.colums.unshift({
      unit: "",
      filter_value: "st",
      domain: "abs_选入日期",
      filter_oper: "不包含",
      source: "fixed_index",
      label: "name",
      filter_keys: "选入日期",
      type: "STR",
      index_name: "选入日期",
      key: "选入日期",
    });
  }

  return newData;
}

let newtest = transformResultsToNewData(results);
console.log(newtest);
