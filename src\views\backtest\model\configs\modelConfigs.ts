export interface Condition {
  id: string;
  text: string;
}

export interface ModelConfig {
  name: string;
  defaultConditions: (selectedDate: string) => Condition[];
  availableConditions: (selectedDate: string, block: string) => Condition[];
}

export const modelConfigs: Record<string, ModelConfig> = {
  创业板龙虎榜: {
    name: "创业板龙虎榜",
    defaultConditions: (selectedDate: string) => [
      { id: "st", text: "去掉st" },
      { id: "block", text: "上市板块等于创业板" },
      { id: "market_value", text: selectedDate + "市值大于25亿且小于400亿" },
      { id: "auction_change", text: selectedDate + "竞价涨幅大于-4%并且小于10.4%" },
      { id: "dragon_tiger_3", text: selectedDate + "之前一个交易日的龙虎榜前四" },
      { id: "dragon_tiger_4", text: selectedDate + "之前一个交易日的板块龙头" },
      { id: "buy_signal", text: selectedDate + "前1个交易日买入信号" },
      { id: "auction_ratio", text: selectedDate + "集合竞价量比大于10且小于40" },
      { id: "auction_type", text: selectedDate + "竞价异动类型" },
    ],
    availableConditions: (selectedDate: string, block: string) => [
      { id: "st", text: "去掉st" },
      { id: "block", text: "上市板块等于" + block },
      { id: "market_value", text: selectedDate + "市值大于25亿且小于400亿" },
      { id: "auction_change", text: selectedDate + "竞价涨幅大于-4%并且小于10.4%" },
      { id: "dragon_tiger_3", text: selectedDate + "之前一个交易日的龙虎榜前四" },
      // { id: "dragon_tiger_2", text: selectedDate + "之前一个交易日的龙虎榜前二" },
      { id: "dragon_tiger_4", text: selectedDate + "之前一个交易日的板块龙头" },
      { id: "close_change", text: selectedDate + "收盘涨跌幅大于-30%" },
      { id: "next_day_open", text: selectedDate + "后一个交易日开盘涨跌幅大于-30%" },
      { id: "next_day_close", text: selectedDate + "后一个交易日收盘涨跌幅大于-30%" },
      { id: "buy_signal", text: selectedDate + "前1个交易日买入信号" },
      { id: "auction_ratio", text: selectedDate + "集合竞价量比大于10且小于40" },
      { id: "auction_type", text: selectedDate + "竞价异动类型" },
    ],
  },

  竞价涨幅模型: {
    name: "竞价涨幅模型",
    defaultConditions: (selectedDate: string) => [{ id: "st", text: "去掉st" }],
    availableConditions: (selectedDate: string, block: string) => [
      { id: "st", text: "去掉st" },
      { id: "block", text: "上市板块等于" + block },
      { id: "market_value", text: selectedDate + "市值大于25亿且小于400亿" },
      { id: "auction_change", text: selectedDate + "竞价涨幅大于-4%并且小于10.4%" },
      { id: "close_change", text: selectedDate + "收盘涨跌幅大于-30%" },
      { id: "next_day_open", text: selectedDate + "后一个交易日开盘涨跌幅大于-30%" },
      { id: "next_day_close", text: selectedDate + "后一个交易日收盘涨跌幅大于-30%" },
      { id: "buy_signal", text: selectedDate + "前1个交易日买入信号" },
      { id: "auction_type", text: selectedDate + "竞价异动类型" },
      { id: "auction_ratio", text: selectedDate + "集合竞价量比大于10且小于40" },
      { id: "auction_ratio2", text: selectedDate + "的大前天交易日没有涨停" },
    ],
  },

  集合竞价量比模型: {
    name: "集合竞价量比模型",
    defaultConditions: (selectedDate: string) => [
      { id: "st", text: "去掉st" },
      { id: "auction_ratio", text: selectedDate + "集合竞价量比大于10且小于40" },
    ],
    availableConditions: (selectedDate: string, block: string) => [
      { id: "st", text: "去掉st" },
      { id: "block", text: "上市板块等于" + block },
      { id: "market_value", text: selectedDate + "市值大于25亿且小于400亿" },
      { id: "auction_ratio", text: selectedDate + "集合竞价量比大于10且小于40" },
      { id: "auction_change", text: selectedDate + "竞价涨幅大于-4%并且小于10.4%" },
      { id: "close_change", text: selectedDate + "收盘涨跌幅大于-30%" },
      { id: "next_day_open", text: selectedDate + "后一个交易日开盘涨跌幅大于-30%" },
      { id: "next_day_close", text: selectedDate + "后一个交易日收盘涨跌幅大于-30%" },
      { id: "buy_signal", text: selectedDate + "前1个交易日买入信号" },
      { id: "auction_type", text: selectedDate + "竞价异动类型" },
    ],
  },
};
