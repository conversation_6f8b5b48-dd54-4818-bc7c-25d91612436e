/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {};

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AppLink: typeof import('./src/components/AppLink/index.vue')['default']
    AppMain: typeof import('./src/layout/components/AppMain/index.vue')['default']
    Breadcrumb: typeof import('./src/components/Breadcrumb/index.vue')['default']
    DeptTree: typeof import('./src/views/system/user/components/DeptTree.vue')['default']
    Dict: typeof import('./src/components/Dict/index.vue')['default']
    DictLabel: typeof import('./src/components/Dict/DictLabel.vue')['default']
    FileUpload: typeof import('./src/components/Upload/FileUpload.vue')['default']
    Fullscreen: typeof import('./src/components/Fullscreen/index.vue')['default']
    Hamburger: typeof import('./src/components/Hamburger/index.vue')['default']
    IconSelect: typeof import('./src/components/IconSelect/index.vue')['default']
    LayoutSelect: typeof import('./src/layout/components/Settings/components/LayoutSelect.vue')['default']
    MultiImageUpload: typeof import('./src/components/Upload/MultiImageUpload.vue')['default']
    NavBar: typeof import('./src/layout/components/NavBar/index.vue')['default']
    NavbarRight: typeof import('./src/layout/components/NavBar/components/NavbarRight.vue')['default']
    Pagination: typeof import('./src/components/Pagination/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Settings: typeof import('./src/layout/components/Settings/index.vue')['default']
    Sidebar: typeof import('./src/layout/components/Sidebar/index.vue')['default']
    SidebarLogo: typeof import('./src/layout/components/Sidebar/components/SidebarLogo.vue')['default']
    SidebarMenu: typeof import('./src/layout/components/Sidebar/components/SidebarMenu.vue')['default']
    SidebarMenuItem: typeof import('./src/layout/components/Sidebar/components/SidebarMenuItem.vue')['default']
    SidebarMenuItemTitle: typeof import('./src/layout/components/Sidebar/components/SidebarMenuItemTitle.vue')['default']
    SidebarMixTopMenu: typeof import('./src/layout/components/Sidebar/components/SidebarMixTopMenu.vue')['default']
    SingleImageUpload: typeof import('./src/components/Upload/SingleImageUpload.vue')['default']
    SizeSelect: typeof import('./src/components/SizeSelect/index.vue')['default']
    TagsView: typeof import('./src/layout/components/TagsView/index.vue')['default']
    UserImport: typeof import('./src/views/system/user/components/UserImport.vue')['default']
    WangEditor: typeof import('./src/components/WangEditor/index.vue')['default']
  }
}
