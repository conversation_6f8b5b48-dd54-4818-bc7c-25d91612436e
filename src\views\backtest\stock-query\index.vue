<template>
  <div class="stock-query-container">
    <stock-query-form @query-results="handleQueryResults" @query-reset="handleQueryReset" />

    <!-- 结果显示 -->
    <!-- <stock-result-table :results="results" :loading="tableLoading" /> -->
    <gpt-table
      :data="queryResults"
      :loading="loading"
      :start-date="startDate"
      :end-date="endDate"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { QueryResultItem } from "@/api/backtest/thsApi";
import StockQueryForm from "./StockQueryForm.vue";
import GptTable from "./gptTable.vue";

defineOptions({
  name: "StockQuery",
});

const queryResults = ref<QueryResultItem[]>([]);
const loading = ref(false);
const startDate = ref("");
const endDate = ref("");

// 处理查询结果
const handleQueryResults = (results: QueryResultItem[], start?: string, end?: string) => {
  loading.value = true;
  // 保存日期范围
  if (start) startDate.value = start;
  if (end) endDate.value = end;

  // 处理结果
  setTimeout(() => {
    queryResults.value = results;
    loading.value = false;
  }, 300);
};

// 重置查询
const handleQueryReset = () => {
  queryResults.value = [];
  startDate.value = "";
  endDate.value = "";
};
</script>

<style scoped>
.stock-query-container {
  padding: 20px;
  padding-bottom: 10vh;
  min-height: calc(100vh - 60px);
}
</style>
