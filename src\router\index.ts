import type { App } from "vue";
import { createRouter, createWebHashHistory, type RouteRecordRaw } from "vue-router";

export const Layout = () => import("@/layout/index.vue");

// 静态路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: "/redirect",
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index.vue"),
      },
    ],
  },

  {
    path: "/login",
    component: () => import("@/views/login/index.vue"),
    meta: { hidden: true },
  },

  {
    path: "/",
    name: "/",
    component: Layout,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        component: () => import("@/views/dashboard/index.vue"),
        name: "Dashboard",
        meta: {
          title: "首页",
          icon: "homepage",
          affix: true,
          keepAlive: true,
        },
      },
      {
        path: "profile",
        name: "Profile",
        component: () => import("@/views/profile/index.vue"),
        meta: { title: "个人中心", icon: "user", hidden: true },
      },
      {
        path: "401",
        component: () => import("@/views/error/401.vue"),
        meta: { hidden: true },
      },
      {
        path: "404",
        component: () => import("@/views/error/404.vue"),
        meta: { hidden: true },
      },
    ],
  },

  {
    path: "/backtest",
    component: Layout,
    redirect: "/backtest/stock-query",
    meta: {
      title: "同花顺回测",
      icon: "api",
      alwaysShow: true,
    },
    children: [
      {
        path: "stock-query",
        component: () => import("@/views/backtest/stock-query/index.vue"),
        name: "StockQuery",
        meta: {
          title: "模型回测",
          icon: "search",
          keepAlive: true,
        },
      },
      {
        path: "main",
        component: () => import("@/views/backtest/main/index.vue"),
        name: "BacktestMain",
        meta: {
          title: "问股查询",
          icon: "chart",
          keepAlive: true,
        },
      },
      {
        path: "model",
        component: () => import("@/views/backtest/model/index.vue"),
        name: "BacktestModel",
        meta: {
          title: "策略模型",
          icon: "setting",
          keepAlive: true,
        },
      },
    ],
  },
];

/**
 * 创建路由
 */
const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes,
  // 刷新时，滚动条位置还原
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

// 全局注册 router
export function setupRouter(app: App<Element>) {
  app.use(router);
}

export default router;
