import { defineMock } from "./base";

export default defineMock([
  {
    url: "menus/routes",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          path: "/system",
          component: "Layout",
          redirect: "/system/user",
          name: "/system",
          meta: {
            title: "系统管理",
            icon: "system",
            hidden: false,
            alwaysShow: false,
            params: null,
          },
          children: [
            {
              path: "user",
              component: "system/user/index",
              name: "User",
              meta: {
                title: "用户管理",
                icon: "el-icon-User",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "role",
              component: "system/role/index",
              name: "Role",
              meta: {
                title: "角色管理",
                icon: "role",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "menu",
              component: "system/menu/index",
              name: "SysMenu",
              meta: {
                title: "菜单管理",
                icon: "menu",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "dept",
              component: "system/dept/index",
              name: "Dept",
              meta: {
                title: "部门管理",
                icon: "tree",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "dict",
              component: "system/dict/index",
              name: "Dict",
              meta: {
                title: "字典管理",
                icon: "dict",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "dict-data",
              component: "system/dict/data",
              name: "DictItem",
              meta: {
                title: "字典数据",
                icon: "",
                hidden: true,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
          ],
        },
        {
          path: "/multi-level",
          component: "Layout",
          name: "/multiLevel",
          meta: {
            title: "多级菜单",
            icon: "cascader",
            hidden: false,
            alwaysShow: true,
            params: null,
          },
          children: [
            {
              path: "multi-level1",
              component: "demo/multi-level/level1",
              name: "MultiLevel1",
              meta: {
                title: "菜单一级",
                icon: "",
                hidden: false,
                alwaysShow: true,
                params: null,
              },
              children: [
                {
                  path: "multi-level2",
                  component: "demo/multi-level/children/level2",
                  name: "MultiLevel2",
                  meta: {
                    title: "菜单二级",
                    icon: "",
                    hidden: false,
                    alwaysShow: false,
                    params: null,
                  },
                  children: [
                    {
                      path: "multi-level3-1",
                      component: "demo/multi-level/children/children/level3-1",
                      name: "MultiLevel31",
                      meta: {
                        title: "菜单三级-1",
                        icon: "",
                        hidden: false,
                        keepAlive: true,
                        alwaysShow: false,
                        params: null,
                      },
                    },
                    {
                      path: "multi-level3-2",
                      component: "demo/multi-level/children/children/level3-2",
                      name: "MultiLevel32",
                      meta: {
                        title: "菜单三级-2",
                        icon: "",
                        hidden: false,
                        keepAlive: true,
                        alwaysShow: false,
                        params: null,
                      },
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          path: "/api",
          component: "Layout",
          name: "/api",
          meta: {
            title: "接口文档",
            icon: "api",
            hidden: false,
            alwaysShow: true,
            params: null,
          },
          children: [
            {
              path: "apifox",
              component: "demo/api/apifox",
              name: "Apifox",
              meta: {
                title: "Apifox",
                icon: "api",
                hidden: false,
                keepAlive: true,
                alwaysShow: false,
                params: null,
              },
            },
          ],
        },
        {
          path: "/doc",
          component: "Layout",
          redirect: "https://juejin.cn/post/7228990409909108793",
          name: "/doc",
          meta: {
            title: "平台文档",
            icon: "document",
            hidden: false,
            alwaysShow: false,
            params: null,
          },
          children: [
            {
              path: "internal-doc",
              component: "demo/internal-doc",
              name: "InternalDoc",
              meta: {
                title: "平台文档(内嵌)",
                icon: "document",
                hidden: false,
                alwaysShow: false,
                params: null,
              },
            },
            {
              path: "https://juejin.cn/post/7228990409909108793",
              name: "Https://juejin.cn/post/7228990409909108793",
              meta: {
                title: "平台文档(外链)",
                icon: "el-icon-link",
                hidden: false,
                alwaysShow: false,
                params: null,
              },
            },
          ],
        },
        // {
        //   path: "/backtest",
        //   component: "Layout",
        //   redirect: "/backtest/stock-query",
        //   name: "/backtest",
        //   meta: {
        //     title: "同花顺回测",
        //     icon: "api",
        //     hidden: false,
        //     alwaysShow: true,
        //     params: null,
        //   },
        //   children: [
        //     {
        //       path: "stock-query",
        //       component: "backtest/stock-query/index",
        //       name: "StockQuery",
        //       meta: {
        //         title: "问股查询",
        //         icon: "search",
        //         hidden: false,
        //         keepAlive: true,
        //         alwaysShow: false,
        //         params: null,
        //       },
        //     },
        //     {
        //       path: "main",
        //       component: "backtest/main/index",
        //       name: "MainBacktest",
        //       meta: {
        //         title: "主板回测",
        //         icon: "chart",
        //         hidden: false,
        //         keepAlive: true,
        //         alwaysShow: false,
        //         params: null,
        //       },
        //     },
        //   ],
        // },
      ],
      msg: "一切ok",
    },
  },

  // 获取菜单树形列表
  {
    url: "menus",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          id: 1,
          parentId: 0,
          name: "系统管理",
          type: "CATALOG",
          routeName: "",
          routePath: "/system",
          component: "Layout",
          sort: 1,
          visible: 1,
          icon: "system",
          redirect: "/system/user",
          perm: null,
          children: [
            {
              id: 2,
              parentId: 1,
              name: "用户管理",
              type: "MENU",
              routeName: "User",
              routePath: "user",
              component: "system/user/index",
              sort: 1,
              visible: 1,
              icon: "el-icon-User",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 105,
                  parentId: 2,
                  name: "用户查询",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 0,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:user:query",
                  children: [],
                },
                {
                  id: 31,
                  parentId: 2,
                  name: "用户新增",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: "",
                  perm: "sys:user:add",
                  children: [],
                },
                {
                  id: 32,
                  parentId: 2,
                  name: "用户编辑",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 2,
                  visible: 1,
                  icon: "",
                  redirect: "",
                  perm: "sys:user:edit",
                  children: [],
                },
                {
                  id: 33,
                  parentId: 2,
                  name: "用户删除",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: "",
                  perm: "sys:user:delete",
                  children: [],
                },
                {
                  id: 88,
                  parentId: 2,
                  name: "重置密码",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 4,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:user:password:reset",
                  children: [],
                },
                {
                  id: 106,
                  parentId: 2,
                  name: "用户导入",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 5,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:user:import",
                  children: [],
                },
                {
                  id: 107,
                  parentId: 2,
                  name: "用户导出",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 6,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:user:export",
                  children: [],
                },
              ],
            },
            {
              id: 3,
              parentId: 1,
              name: "角色管理",
              type: "MENU",
              routeName: "Role",
              routePath: "role",
              component: "system/role/index",
              sort: 2,
              visible: 1,
              icon: "role",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 70,
                  parentId: 3,
                  name: "角色新增",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:role:add",
                  children: [],
                },
                {
                  id: 71,
                  parentId: 3,
                  name: "角色编辑",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 2,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:role:edit",
                  children: [],
                },
                {
                  id: 72,
                  parentId: 3,
                  name: "角色删除",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:role:delete",
                  children: [],
                },
              ],
            },
            {
              id: 4,
              parentId: 1,
              name: "菜单管理",
              type: "MENU",
              routeName: "Menu",
              routePath: "menu",
              component: "system/menu/index",
              sort: 3,
              visible: 1,
              icon: "menu",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 73,
                  parentId: 4,
                  name: "菜单新增",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:menu:add",
                  children: [],
                },
                {
                  id: 74,
                  parentId: 4,
                  name: "菜单编辑",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:menu:edit",
                  children: [],
                },
                {
                  id: 75,
                  parentId: 4,
                  name: "菜单删除",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:menu:delete",
                  children: [],
                },
              ],
            },
            {
              id: 5,
              parentId: 1,
              name: "部门管理",
              type: "MENU",
              routeName: "Dept",
              routePath: "dept",
              component: "system/dept/index",
              sort: 4,
              visible: 1,
              icon: "tree",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 76,
                  parentId: 5,
                  name: "部门新增",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dept:add",
                  children: [],
                },
                {
                  id: 77,
                  parentId: 5,
                  name: "部门编辑",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 2,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dept:edit",
                  children: [],
                },
                {
                  id: 78,
                  parentId: 5,
                  name: "部门删除",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dept:delete",
                  children: [],
                },
              ],
            },
            {
              id: 6,
              parentId: 1,
              name: "字典管理",
              type: "MENU",
              routeName: "Dict",
              routePath: "dict",
              component: "system/dict/index",
              sort: 5,
              visible: 1,
              icon: "dict",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 79,
                  parentId: 6,
                  name: "字典新增",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict:add",
                  children: [],
                },
                {
                  id: 81,
                  parentId: 6,
                  name: "字典编辑",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 2,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict_type:edit",
                  children: [],
                },
                {
                  id: 84,
                  parentId: 6,
                  name: "字典删除",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 3,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict_type:delete",
                  children: [],
                },
              ],
            },
            {
              id: 135,
              parentId: 1,
              name: "字典数据",
              type: "MENU",
              routeName: "DictData",
              routePath: "dict-data",
              component: "system/dict/data",
              sort: 6,
              visible: 0,
              icon: "",
              redirect: null,
              perm: null,
              children: [
                {
                  id: 136,
                  parentId: 135,
                  name: "字典数据新增",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 4,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict-data:add",
                  children: [],
                },
                {
                  id: 137,
                  parentId: 135,
                  name: "字典数据编辑",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 5,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict-data:edit",
                  children: [],
                },
                {
                  id: 138,
                  parentId: 135,
                  name: "字典数据删除",
                  type: "BUTTON",
                  routeName: null,
                  routePath: "",
                  component: null,
                  sort: 6,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: "sys:dict-data:delete",
                  children: [],
                },
              ],
            },
          ],
        },
        {
          id: 40,
          parentId: 0,
          name: "接口文档",
          type: "CATALOG",
          routeName: null,
          routePath: "/api",
          component: "Layout",
          sort: 7,
          visible: 1,
          icon: "api",
          redirect: "",
          perm: null,
          children: [
            {
              id: 41,
              parentId: 40,
              name: "Apifox",
              type: "MENU",
              routeName: null,
              routePath: "apifox",
              component: "demo/api/apifox",
              sort: 1,
              visible: 1,
              icon: "api",
              redirect: "",
              perm: null,
              children: [],
            },
          ],
        },
        {
          id: 26,
          parentId: 0,
          name: "平台文档",
          type: "CATALOG",
          routeName: null,
          routePath: "/doc",
          component: "Layout",
          sort: 8,
          visible: 1,
          icon: "document",
          redirect: "https://juejin.cn/post/7228990409909108793",
          perm: null,
          children: [
            {
              id: 102,
              parentId: 26,
              name: "平台文档(内嵌)",
              type: "EXTLINK",
              routeName: null,
              routePath: "internal-doc",
              component: "demo/internal-doc",
              sort: 1,
              visible: 1,
              icon: "document",
              redirect: "",
              perm: null,
              children: [],
            },
            {
              id: 30,
              parentId: 26,
              name: "平台文档(外链)",
              type: "EXTLINK",
              routeName: null,
              routePath: "https://juejin.cn/post/7228990409909108793",
              component: "",
              sort: 2,
              visible: 1,
              icon: "link",
              redirect: "",
              perm: null,
              children: [],
            },
          ],
        },
        {
          id: 20,
          parentId: 0,
          name: "多级菜单",
          type: "CATALOG",
          routeName: null,
          routePath: "/multi-level",
          component: "Layout",
          sort: 9,
          visible: 1,
          icon: "cascader",
          redirect: "",
          perm: null,
          children: [
            {
              id: 21,
              parentId: 20,
              name: "菜单一级",
              type: "MENU",
              routeName: null,
              routePath: "multi-level1",
              component: "demo/multi-level/level1",
              sort: 1,
              visible: 1,
              icon: "",
              redirect: "",
              perm: null,
              children: [
                {
                  id: 22,
                  parentId: 21,
                  name: "菜单二级",
                  type: "MENU",
                  routeName: null,
                  routePath: "multi-level2",
                  component: "demo/multi-level/children/level2",
                  sort: 1,
                  visible: 1,
                  icon: "",
                  redirect: null,
                  perm: null,
                  children: [
                    {
                      id: 23,
                      parentId: 22,
                      name: "菜单三级-1",
                      type: "MENU",
                      routeName: null,
                      routePath: "multi-level3-1",
                      component: "demo/multi-level/children/children/level3-1",
                      sort: 1,
                      visible: 1,
                      icon: "",
                      redirect: "",
                      perm: null,
                      children: [],
                    },
                    {
                      id: 24,
                      parentId: 22,
                      name: "菜单三级-2",
                      type: "MENU",
                      routeName: null,
                      routePath: "multi-level3-2",
                      component: "demo/multi-level/children/children/level3-2",
                      sort: 2,
                      visible: 1,
                      icon: "",
                      redirect: "",
                      perm: null,
                      children: [],
                    },
                  ],
                },
              ],
            },
          ],
        },
        // {
        //   id: 200,
        //   parentId: 0,
        //   name: "同花顺回测",
        //   type: "CATALOG",
        //   routeName: null,
        //   routePath: "/backtest",
        //   component: "Layout",
        //   sort: 10,
        //   visible: 1,
        //   icon: "api",
        //   redirect: "/backtest/stock-query",
        //   perm: null,
        //   children: [
        //     {
        //       id: 201,
        //       parentId: 200,
        //       name: "主板回测",
        //       type: "MENU",
        //       routeName: "MainBacktest",
        //       routePath: "main",
        //       component: "backtest/main/index",
        //       sort: 1,
        //       visible: 1,
        //       icon: "chart",
        //       redirect: null,
        //       perm: null,
        //       children: [],
        //     },
        //   ],
        // },
      ],
      msg: "一切ok",
    },
  },

  // 获取菜单树形下拉列表
  {
    url: "menus/options",
    method: ["GET"],
    body: {
      code: "00000",
      data: [
        {
          value: "1",
          label: "系统管理",
          children: [
            {
              value: "2",
              label: "用户管理",
              children: [
                {
                  value: "105",
                  label: "用户查询",
                },
                {
                  value: "31",
                  label: "用户新增",
                },
                {
                  value: "32",
                  label: "用户编辑",
                },
                {
                  value: "33",
                  label: "用户删除",
                },
                {
                  value: "88",
                  label: "重置密码",
                },
                {
                  value: "106",
                  label: "用户导入",
                },
                {
                  value: "107",
                  label: "用户导出",
                },
              ],
            },
            {
              value: "3",
              label: "角色管理",
              children: [
                {
                  value: "139",
                  label: "角色查询",
                },
                {
                  value: "70",
                  label: "角色新增",
                },
                {
                  value: "71",
                  label: "角色编辑",
                },
                {
                  value: "72",
                  label: "角色删除",
                },
              ],
            },
            {
              value: "4",
              label: "菜单管理",
              children: [
                {
                  value: "73",
                  label: "菜单新增",
                },
                {
                  value: "140",
                  label: "菜单查询",
                },
                {
                  value: "75",
                  label: "菜单删除",
                },
                {
                  value: "74",
                  label: "菜单编辑",
                },
              ],
            },
            {
              value: "5",
              label: "部门管理",
              children: [
                {
                  value: "76",
                  label: "部门新增",
                },
                {
                  value: "141",
                  label: "部门查询",
                },
                {
                  value: "77",
                  label: "部门编辑",
                },
                {
                  value: "78",
                  label: "部门删除",
                },
              ],
            },
            {
              value: "6",
              label: "字典管理",
              children: [
                {
                  value: "79",
                  label: "字典新增",
                },
                {
                  value: "142",
                  label: "字典查询",
                },
                {
                  value: "81",
                  label: "字典编辑",
                },
                {
                  value: "84",
                  label: "字典删除",
                },
              ],
            },
            {
              value: "117",
              label: "系统日志",
            },
            {
              value: "135",
              label: "字典数据",
              children: [
                {
                  value: "143",
                  label: "字典数据查询",
                },
                {
                  value: "136",
                  label: "字典数据新增",
                },
                {
                  value: "137",
                  label: "字典数据编辑",
                },
                {
                  value: "138",
                  label: "字典数据删除",
                },
              ],
            },
            {
              value: "120",
              label: "系统配置",
              children: [
                {
                  value: "121",
                  label: "系统配置查询",
                },
                {
                  value: "122",
                  label: "系统配置新增",
                },
                {
                  value: "123",
                  label: "系统配置修改",
                },
                {
                  value: "124",
                  label: "系统配置删除",
                },
                {
                  value: "125",
                  label: "系统配置刷新",
                },
              ],
            },
            {
              value: "126",
              label: "通知公告",
              children: [
                {
                  value: "127",
                  label: "通知查询",
                },
                {
                  value: "128",
                  label: "通知新增",
                },
                {
                  value: "129",
                  label: "通知编辑",
                },
                {
                  value: "130",
                  label: "通知删除",
                },
                {
                  value: "133",
                  label: "通知发布",
                },
                {
                  value: "134",
                  label: "通知撤回",
                },
              ],
            },
          ],
        },
        {
          value: "118",
          label: "系统工具",
          children: [
            {
              value: "119",
              label: "代码生成",
            },
          ],
        },
        {
          value: "40",
          label: "接口文档",
          children: [
            {
              value: "41",
              label: "Apifox",
            },
          ],
        },
        {
          value: "26",
          label: "平台文档",
          children: [
            {
              value: "102",
              label: "document",
            },
            {
              value: "30",
              label: "平台文档(外链)",
            },
          ],
        },
        {
          value: "20",
          label: "多级菜单",
          children: [
            {
              value: "21",
              label: "菜单一级",
              children: [
                {
                  value: "22",
                  label: "菜单二级",
                  children: [
                    {
                      value: "23",
                      label: "菜单三级-1",
                    },
                    {
                      value: "24",
                      label: "菜单三级-2",
                    },
                  ],
                },
              ],
            },
          ],
        },
        // {
        //   value: "200",
        //   label: "同花顺回测",
        //   children: [
        //     {
        //       value: "201",
        //       label: "主板回测",
        //     },
        //   ],
        // },
      ],

      msg: "一切ok",
    },
  },

  // 新增菜单
  {
    url: "menus",
    method: ["POST"],
    body({ body }) {
      return {
        code: "00000",
        data: null,
        msg: "新增菜单" + body.name + "成功",
      };
    },
  },

  // 获取菜单表单数据
  {
    url: "menus/:id/form",
    method: ["GET"],
    body: ({ params }) => {
      return {
        code: "00000",
        data: menuMap[params.id],
        msg: "一切ok",
      };
    },
  },

  // 修改菜单
  {
    url: "menus/:id",
    method: ["PUT"],
    body({ body }) {
      return {
        code: "00000",
        data: null,
        msg: "修改菜单" + body.name + "成功",
      };
    },
  },

  // 删除菜单
  {
    url: "menus/:id",
    method: ["DELETE"],
    body({ params }) {
      return {
        code: "00000",
        data: null,
        msg: "删除菜单" + params.id + "成功",
      };
    },
  },
]);

// 菜单映射表数据
const menuMap: Record<string, any> = {
  1: {
    id: 1,
    parentId: 0,
    name: "系统管理",
    type: "CATALOG",
    routeName: "",
    routePath: "/system",
    component: "Layout",
    perm: null,
    visible: 1,
    sort: 1,
    icon: "system",
    redirect: "/system/user",
    keepAlive: null,
    alwaysShow: null,
    params: null,
  },
  2: {
    id: 2,
    parentId: 1,
    name: "用户管理",
    type: "MENU",
    routeName: "User",
    routePath: "user",
    component: "system/user/index",
    perm: null,
    visible: 1,
    sort: 1,
    icon: "user",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null,
  },
  3: {
    id: 3,
    parentId: 1,
    name: "角色管理",
    type: "MENU",
    routeName: "Role",
    routePath: "role",
    component: "system/role/index",
    perm: null,
    visible: 1,
    sort: 2,
    icon: "role",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null,
  },
  4: {
    id: 4,
    parentId: 1,
    name: "菜单管理",
    type: "MENU",
    routeName: "Menu",
    routePath: "menu",
    component: "system/menu/index",
    perm: null,
    visible: 1,
    sort: 3,
    icon: "menu",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null,
  },
  5: {
    id: 5,
    parentId: 1,
    name: "部门管理",
    type: "MENU",
    routeName: "Dept",
    routePath: "dept",
    component: "system/dept/index",
    perm: null,
    visible: 1,
    sort: 4,
    icon: "tree",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null,
  },
  6: {
    id: 6,
    parentId: 1,
    name: "字典管理",
    type: "MENU",
    routeName: "Dict",
    routePath: "dict",
    component: "system/dict/index",
    perm: null,
    visible: 1,
    sort: 5,
    icon: "dict",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null,
  },
  200: {
    id: 200,
    parentId: 0,
    name: "同花顺回测",
    type: "CATALOG",
    routeName: "",
    routePath: "/backtest",
    component: "Layout",
    perm: null,
    visible: 1,
    sort: 10,
    icon: "api",
    redirect: "/backtest/stock-query",
    keepAlive: null,
    alwaysShow: null,
    params: null,
  },
  201: {
    id: 201,
    parentId: 200,
    name: "主板回测",
    type: "MENU",
    routeName: "MainBacktest",
    routePath: "main",
    component: "backtest/main/index",
    perm: null,
    visible: 1,
    sort: 1,
    icon: "chart",
    redirect: null,
    keepAlive: 1,
    alwaysShow: null,
  },
};
